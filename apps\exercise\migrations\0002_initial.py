# Generated by Django 5.1.3 on 2024-11-21 08:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('assets', '0002_initial'),
        ('exercise', '0001_initial'),
        ('virus', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='exercise',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_exercises', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddField(
            model_name='exercise',
            name='target_assets',
            field=models.ManyToManyField(help_text='选择本次演练的目标资产', related_name='exercises', to='assets.asset', verbose_name='目标资产'),
        ),
        migrations.AddField(
            model_name='exercise',
            name='target_groups',
            field=models.ManyToManyField(blank=True, help_text='选择本次演练的目标资产组', related_name='exercises', to='assets.assetgroup', verbose_name='目标资产组'),
        ),
        migrations.AddField(
            model_name='exercise',
            name='virus',
            field=models.ForeignKey(help_text='选择本次演练使用的病毒样本', on_delete=django.db.models.deletion.PROTECT, related_name='exercises', to='virus.virus', verbose_name='使用病毒'),
        ),
        migrations.AddField(
            model_name='exerciselog',
            name='asset',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='exercise_logs', to='assets.asset', verbose_name='相关资产'),
        ),
        migrations.AddField(
            model_name='exerciselog',
            name='exercise',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='exercise.exercise', verbose_name='所属演练'),
        ),
    ]
