from rest_access_policy import AccessPolicy


class SelfAccessPolicy(AccessPolicy):
    def is_superuser(self, request, view, action):  # NOQA
        return request.user.is_superuser

    def is_create_by(self, request, view, action):  # NOQA
        obj = view.get_object()
        if not obj.created_by:
            return False
        return request.user.id == obj.created_by.id  # NOQA

    def always_allow(self, request, view, action):  # NOQA
        return True and not request.user.is_anonymous

    statements = [
        {
            "condition": "is_superuser",  # 超级管理员允许执行所有操作
            "principal": "*",
            "effect": "allow",
            "action": ["*"],
        },
        {
            "condition": "is_create_by",  # 普通用户只能操作自己创建的内容
            "principal": "*",
            "effect": "allow",
            "action": [
                "retrieve", "update", "destroy",
                "infection_statistics", "device_statistics", "department", "send_email_records",
                "phishing_email_sending_overview", "open_email_data_statistics", "phishing_email_click_stats",
                "infection_overview"
                ],
        },
        {
            "condition": "always_allow",  # 允许所有用户查看列表
            "principal": "*",
            "effect": "allow",
            "action": ["list"],
        },
        {
            "condition": "always_allow",  # 允许所有用户进行创建操作
            "principal": "*",
            "effect": "allow",
            "action": ["create"],
        }
    ]
