# Generated by Django 5.1.3 on 2024-12-21 15:49

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('infection', '0003_alter_infectionrecord_options_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='infectionrecord',
            name='asset',
        ),
        migrations.RemoveField(
            model_name='infectionrecord',
            name='data_loss',
        ),
        migrations.RemoveField(
            model_name='infectionrecord',
            name='description',
        ),
        migrations.RemoveField(
            model_name='infectionrecord',
            name='encrypted_files',
        ),
        migrations.RemoveField(
            model_name='infectionrecord',
            name='exercise',
        ),
        migrations.RemoveField(
            model_name='infectionrecord',
            name='infection_time',
        ),
        migrations.RemoveField(
            model_name='infectionrecord',
            name='ransom_amount',
        ),
        migrations.RemoveField(
            model_name='infectionrecord',
            name='status',
        ),
        migrations.AddField(
            model_name='infectionrecord',
            name='system_time',
            field=models.CharField(default=django.utils.timezone.now, max_length=255, verbose_name='系统时间'),
        ),
        migrations.AlterField(
            model_name='dataleakage',
            name='file_types',
            field=models.JSONField(help_text='各类型���件的数量统计，如{"doc": 5, "pdf": 3}', verbose_name='文件类型统计'),
        ),
        migrations.AlterField(
            model_name='infectionrecord',
            name='client_id',
            field=models.CharField(max_length=36, verbose_name='客户端ID'),
        ),
        migrations.AlterField(
            model_name='infectionrecord',
            name='exec_path',
            field=models.CharField(max_length=1024, verbose_name='执行路径'),
        ),
        migrations.AlterField(
            model_name='infectionrecord',
            name='hostname',
            field=models.CharField(max_length=255, verbose_name='主机名'),
        ),
        migrations.AlterField(
            model_name='infectionrecord',
            name='ip_address',
            field=models.GenericIPAddressField(default='0.0.0.0', verbose_name='IP地址'),
        ),
        migrations.AlterField(
            model_name='infectionrecord',
            name='location',
            field=models.CharField(max_length=255, verbose_name='位置信息'),
        ),
        migrations.AlterField(
            model_name='infectionrecord',
            name='username',
            field=models.CharField(max_length=255, verbose_name='用户名'),
        ),
    ]
