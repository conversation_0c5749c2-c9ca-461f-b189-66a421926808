from openpyxl import load_workbook


def get_sheetnames(filename):
    """
    获取xlsx中所有sheetname
    """
    wb = load_workbook(filename)
    return wb.sheetnames


def read_sheet(sheetobj, col_keys, skip_row):
    """
    获取sheet中的数据，返回[{},{}]
    """
    max_col = sheetobj.max_column
    max_row = sheetobj.max_row
    if len(col_keys) > max_col:
        raise RuntimeError(
            "KEY值长度不够"
        )
    datalist = []
    for row_idx in range(max_row):
        if row_idx < skip_row:
            continue
        t_row_dict = {}
        for cur_idx, cur_data in enumerate(col_keys, 1):
            # print(cur_data)
            if cur_data is None:
                continue
            col_value = sheetobj.cell(row=row_idx + 1, column=cur_idx).value
            if col_value is None:
                t_row_dict[cur_data] = None
            else:
                t_row_dict[cur_data] = str(col_value).strip()
        # 过滤掉整行None的数据
        if list(filter(None, t_row_dict.values())):
            datalist.append(t_row_dict)
    return datalist


def get_data_by_sheetname(filename, sheetname=None, col_keys=None, skip_row=0):
    """
    读取excel中指定sheet的数据，按行返回，需要指定每行数据的key
    """
    if not col_keys:
        raise RuntimeError("col_keys 不可为空，应该为 [keyname, keyname, keyname] 结构")
    if not sheetname:
        sheetname_list = get_sheetnames(filename)
        if len(sheetname_list) != 1:
            raise RuntimeError("sheetname 不可为空")
        sheetname = sheetname_list[0]
    wb = load_workbook(filename)
    if sheetname in wb.sheetnames:
        return read_sheet(wb[sheetname], col_keys, skip_row)
    return []


if __name__ == '__main__':
    keys = ["username", "phone", "password", "email", "affiliation"]
    data = get_data_by_sheetname('用户导入模板.xlsx', '用户', keys, skip_row=8)
    print(data)
