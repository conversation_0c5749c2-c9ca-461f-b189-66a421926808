# Generated by Django 5.1.3 on 2024-11-21 08:50

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AlertHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('level', models.CharField(choices=[('IN', '信息'), ('WA', '警告'), ('ER', '错误'), ('CR', '严重')], help_text='告警的严重程度', max_length=2, verbose_name='告警级别')),
                ('title', models.CharField(help_text='告警的简要描述', max_length=200, verbose_name='告警标题')),
                ('content', models.TextField(help_text='告警的详细信息', verbose_name='告警内容')),
                ('is_read', models.BooleanField(default=False, help_text='告警是否已被查看', verbose_name='是否已读')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='告警产生的时间', verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '告警历史',
                'verbose_name_plural': '告警历史',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AssetStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('NM', '正常'), ('IN', '已感染'), ('EN', '已加密'), ('RC', '已恢复'), ('OF', '已下线')], max_length=2)),
                ('cpu_usage', models.FloatField(blank=True, null=True)),
                ('memory_usage', models.FloatField(blank=True, null=True)),
                ('disk_usage', models.FloatField(blank=True, null=True)),
                ('network_traffic', models.FloatField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='DashboardConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(help_text='配置项的唯一标识名称', max_length=100, unique=True, verbose_name='配置名称')),
                ('config_type', models.CharField(choices=[('CH', '图表配置'), ('AL', '告警配置'), ('DI', '显示配置'), ('OT', '其他配置')], help_text='配置项的分类', max_length=2, verbose_name='配置类型')),
                ('value', models.JSONField(help_text='配置项的值，使用JSON格式存储', verbose_name='配置值')),
                ('description', models.TextField(blank=True, help_text='配置项的详细说明', verbose_name='描述')),
                ('is_active', models.BooleanField(default=True, help_text='配置项是否生效', verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '数据看板配置',
                'verbose_name_plural': '数据看板配置',
                'ordering': ['config_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='DashboardMetric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('metric_time', models.DateTimeField(default=django.utils.timezone.now, help_text='指标统计的时间点', verbose_name='统计时间')),
                ('total_assets', models.IntegerField(default=0, help_text='目标资产总数', verbose_name='资产总数')),
                ('infected_count', models.IntegerField(default=0, help_text='已感染资产数量', verbose_name='感染数量')),
                ('encrypted_count', models.IntegerField(default=0, help_text='文件被加密的资产数量', verbose_name='加密数量')),
                ('total_data_loss', models.BigIntegerField(default=0, help_text='数据丢失总量（字节）', verbose_name='总数据丢失量')),
                ('endpoint_infected', models.IntegerField(default=0, help_text='感染的终端数量', verbose_name='终端感染数')),
                ('server_infected', models.IntegerField(default=0, help_text='感染的服务器数量', verbose_name='服务器感染数')),
            ],
            options={
                'verbose_name': '数据看板指标',
                'verbose_name_plural': '数据看板指标',
                'ordering': ['-metric_time'],
                'get_latest_by': 'metric_time',
            },
        ),
        migrations.CreateModel(
            name='InfectionRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('status', models.CharField(choices=[('IN', '已感染'), ('EN', '已加密'), ('DE', '已解密'), ('CL', '已清理')], default='IN', max_length=2, verbose_name='感染状态')),
                ('infection_time', models.DateTimeField(auto_now_add=True, verbose_name='感染时间')),
                ('encrypted_files', models.IntegerField(default=0, verbose_name='加密文件数')),
                ('data_loss', models.BigIntegerField(default=0, verbose_name='数据丢失量(字节)')),
                ('ransom_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='勒索金额')),
                ('description', models.TextField(blank=True, verbose_name='详细描述')),
            ],
            options={
                'verbose_name': '感染记录',
                'verbose_name_plural': '感染记录',
                'ordering': ['-infection_time'],
            },
        ),
        migrations.CreateModel(
            name='InfectionTrend',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('infected_count', models.IntegerField(default=0)),
                ('encrypted_count', models.IntegerField(default=0)),
                ('recovered_count', models.IntegerField(default=0)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='PhishingRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('ip_address', models.GenericIPAddressField(verbose_name='来源IP')),
                ('click_time', models.DateTimeField(auto_now_add=True, verbose_name='点击时间')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('referrer', models.URLField(blank=True, verbose_name='来源URL')),
            ],
            options={
                'verbose_name': '钓鱼记录',
                'verbose_name_plural': '钓鱼记录',
                'ordering': ['-click_time'],
            },
        ),
    ]
