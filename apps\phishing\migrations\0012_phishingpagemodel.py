# Generated by Django 5.1.3 on 2024-12-18 16:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('phishing', '0011_emailtemplatefilemodel_file_size'),
    ]

    operations = [
        migrations.CreateModel(
            name='PhishingPageModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=200, verbose_name='页面名称')),
                ('redirect_address', models.CharField(blank=True, max_length=300, null=True, verbose_name='重定向地址')),
                ('request_url', models.Char<PERSON>ield(blank=True, max_length=300, null=True, verbose_name='克隆网站')),
                ('content', models.TextField(blank=True, null=True, verbose_name='内容')),
                ('is_capture_submitted_data', models.BooleanField(default=False, verbose_name='捕获提交数据')),
                ('is_capture_password', models.BooleanField(default=False, verbose_name='捕获密码')),
            ],
            options={
                'verbose_name': '钓鱼页面',
                'verbose_name_plural': '钓鱼页面',
                'db_table': 'ls_phishing_page',
                'ordering': ['-id'],
            },
        ),
    ]
