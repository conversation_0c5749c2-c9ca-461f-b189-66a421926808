# Generated by Django 5.1.3 on 2025-02-21 14:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('exercise', '0008_rename_celery_task_id_to_task_id'),
        ('system', '0003_alter_systemconfig_config_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotifyModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('content', models.TextField(blank=True, null=True, verbose_name='消息内容')),
                ('exercise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='exercise.exercise', verbose_name='项目模型')),
            ],
            options={
                'verbose_name': '系统消息',
                'verbose_name_plural': '系统消息',
                'db_table': 'ls_notify',
                'ordering': ['exercise'],
            },
        ),
    ]
