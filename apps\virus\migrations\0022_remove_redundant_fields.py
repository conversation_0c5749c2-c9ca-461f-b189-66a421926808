# Generated manually to remove redundant fields from Virus model

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('virus', '0021_remove_virus_behavior_fields'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='virus',
            name='outbreak_start_time',
        ),
        migrations.RemoveField(
            model_name='virus',
            name='outbreak_end_time',
        ),
        migrations.RemoveField(
            model_name='virus',
            name='encryption_files_suffix',
        ),
        migrations.RemoveField(
            model_name='virus',
            name='is_negotiation_feature',
        ),
        migrations.RemoveField(
            model_name='virus',
            name='enable_virus_evolution',
        ),
    ]
