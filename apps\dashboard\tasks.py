from django.utils import timezone
from django.db.models import Count, Q
from datetime import timedelta

from apps.exercise.models import Exercise
from apps.assets.models import Asset, AssetGroup
from .models import DashboardStatistics, ExerciseStatistics, AssetStatistics

def update_dashboard_statistics():
    """更新数据看板统计数据"""
    from apps.infection.models import InfectionRecord

    today = timezone.now().date()

    # 更新总体统计
    stats, _ = DashboardStatistics.objects.get_or_create(date=today)
    stats.total_exercises = Exercise.objects.count()
    stats.active_exercises = Exercise.objects.filter(status=Exercise.Status.RUNNING).count()
    stats.finished_exercises = Exercise.objects.filter(status=Exercise.Status.FINISHED).count()
    stats.total_assets = Asset.objects.count()

    # 通过感染记录获取感染资产数
    infected_asset_ids = InfectionRecord.objects.filter(
        status='IN'  # 已感染状态
    ).values_list('device_id', flat=True).distinct()
    stats.infected_assets = len(infected_asset_ids)

    stats.total_asset_groups = AssetGroup.objects.count()
    stats.save()

    # 更新演练统计
    for exercise in Exercise.objects.filter(
        Q(status=Exercise.Status.RUNNING) |
        Q(status=Exercise.Status.FINISHED)
    ):
        exercise_stats, _ = ExerciseStatistics.objects.get_or_create(
            exercise=exercise,
            date=today
        )

        # 计算感染数和成功率
        target_asset_ids = []
        for group in exercise.target_groups.all():
            # 通过asset关系获取该组中的所有资产ID
            asset_ids = group.asset.all().values_list('id', flat=True)
            target_asset_ids.extend(asset_ids)
        
        total_assets = len(target_asset_ids)

        # 通过感染记录获取目标资产中的感染数
        infected_assets = InfectionRecord.objects.filter(
            device_id__in=target_asset_ids,
            status='IN'  # 已感染状态
        ).values('device_id').distinct().count()

        exercise_stats.infected_count = infected_assets
        if total_assets > 0:
            exercise_stats.success_rate = (infected_assets / total_assets) * 100

        # 计算持续时间
        if exercise.start_time:
            end_time = exercise.end_time or timezone.now()
            exercise_stats.duration = end_time - exercise.start_time

        exercise_stats.save()

    # 更新资产组统计
    for asset_group in AssetGroup.objects.all():
        asset_stats, _ = AssetStatistics.objects.get_or_create(
            asset_group=asset_group,
            date=today
        )

        assets = asset_group.asset.all()  # 注意这里是asset不是assets
        asset_stats.total_assets = assets.count()
        asset_stats.online_assets = assets.filter(is_active=True).count()

        # 通过感染记录获取该资产组中的感染资产数
        asset_ids = assets.values_list('id', flat=True)
        infected_count = InfectionRecord.objects.filter(
            device_id__in=asset_ids,
            status='IN'  # 已感染状态
        ).values('device_id').distinct().count()
        asset_stats.infected_assets = infected_count

        asset_stats.save()

def cleanup_old_statistics():
    """清理30天前的统计数据"""
    threshold = timezone.now().date() - timedelta(days=30)

    DashboardStatistics.objects.filter(date__lt=threshold).delete()
    ExerciseStatistics.objects.filter(date__lt=threshold).delete()
    AssetStatistics.objects.filter(date__lt=threshold).delete()