# Generated by Django 5.1.3 on 2025-03-18 15:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('phishing', '0023_formsubmitmodel'),
    ]

    operations = [
        migrations.AddField(
            model_name='formsubmitmodel',
            name='browser_info',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='浏览器信息'),
        ),
        migrations.AddField(
            model_name='formsubmitmodel',
            name='os_info',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='操作系统信息'),
        ),
        migrations.AddField(
            model_name='formsubmitmodel',
            name='status',
            field=models.Char<PERSON>ield(choices=[('CLICK', '点击访问'), ('SUBMIT', '提交表单数据')], default='CLICK', max_length=20, verbose_name='钓鱼状态'),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='formsubmitmodel',
            name='for_data',
            field=models.J<PERSON><PERSON>ield(blank=True, default=dict, null=True, verbose_name='表单提交信息'),
        ),
    ]
