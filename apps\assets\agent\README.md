# 资产监控代理

这是一个用于收集和报告系统资产信息的监控代理程序。它可以收集系统性能指标、进程信息等，并将这些信息定期上报到中央服务器。

## 功能特性

- 自动收集系统信息（主机名、IP地址、MAC地址等）
- 监控系统性能指标（CPU使用率、内存使用率、磁盘使用率等）
- 收集进程信息
- 定期向服务器报告状态
- 支持自动注册资产

## 安装要求

```bash
pip install -r requirements.txt
```

## 构建可执行文件

运行以下命令构建独立的可执行文件：

```bash
python build.py
```

构建完成后，可执行文件将位于 `dist` 目录中。

## 使用方法

### 直接运行Python脚本

```bash
python asset_monitor.py --server http://your-server-url --token your-api-token --interval 60
```

### 运行打包后的可执行文件

```bash
asset_monitor.exe --server http://your-server-url --token your-api-token --interval 60
```

### 参数说明

- `--server`: 必需，API服务器地址
- `--token`: 可选，API认证令牌
- `--interval`: 可选，数据收集间隔（秒），默认60秒

## 日志

程序运行时会自动生成日志，包含以下信息：
- 程序启动和停止
- 资产注册状态
- 数据上报状态
- 错误信息

## 安全说明

1. 建议使用HTTPS协议与服务器通信
2. 妥善保管API令牌
3. 确保只收集必要的系统信息
4. 定期检查和更新监控代理

## 故障排除

1. 如果无法连接服务器：
   - 检查服务器URL是否正确
   - 确认网络连接是否正常
   - 验证防火墙设置

2. 如果资产注册失败：
   - 确认API令牌是否有效
   - 检查服务器日志

3. 如果数据收集出错：
   - 确保程序有足够的系统权限
   - 检查系统资源是否可用

## 开发说明

1. 添加新的监控指标：
   - 在相应的方法中添加新的数据收集逻辑
   - 更新数据上报格式

2. 自定义数据收集：
   - 修改 `get_system_info()` 和 `get_performance_metrics()` 方法
   - 添加新的数据收集方法

3. 调整上报策略：
   - 修改 `report_status()` 方法
   - 调整数据上报的频率和重试逻辑
