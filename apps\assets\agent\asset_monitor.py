import psutil
import platform
import socket
import uuid
import json
import time
import requests
import logging
from datetime import datetime

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('AssetMonitor')

class AssetMonitor:
    def __init__(self, server_url, api_token=None, interval=60):
        """
        初始化资产监控器
        :param server_url: API服务器地址
        :param api_token: API认证令牌
        :param interval: 数据收集间隔（秒）
        """
        self.server_url = server_url.rstrip('/')
        self.api_token = api_token
        self.interval = interval
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Token {api_token}' if api_token else ''
        }

    def get_system_info(self):
        """获取系统基本信息"""
        try:
            return {
                'hostname': socket.gethostname(),
                'ip_address': socket.gethostbyname(socket.gethostname()),
                'mac_address': ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                                     for elements in range(0,2*6,2)][::-1]),
                'os_type': platform.system(),
                'os_version': platform.version(),
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine()
            }
        except Exception as e:
            logger.error(f"Error getting system info: {str(e)}")
            return {}

    def get_performance_metrics(self):
        """获取性能指标"""
        try:
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            net_io = psutil.net_io_counters()
            
            return {
                'cpu_usage': cpu_usage,
                'memory_usage': memory.percent,
                'memory_total': memory.total,
                'memory_available': memory.available,
                'disk_usage': disk.percent,
                'disk_total': disk.total,
                'disk_free': disk.free,
                'network_bytes_sent': net_io.bytes_sent,
                'network_bytes_recv': net_io.bytes_recv,
                'process_count': len(psutil.pids())
            }
        except Exception as e:
            logger.error(f"Error getting performance metrics: {str(e)}")
            return {}

    def get_process_info(self):
        """获取进程信息"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent']):
                try:
                    pinfo = proc.info
                    processes.append({
                        'pid': pinfo['pid'],
                        'name': pinfo['name'],
                        'username': pinfo['username'],
                        'cpu_percent': pinfo['cpu_percent'],
                        'memory_percent': pinfo['memory_percent']
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass
            return processes
        except Exception as e:
            logger.error(f"Error getting process info: {str(e)}")
            return []

    def report_status(self):
        """上报状态信息到服务器"""
        try:
            data = {
                'system_info': self.get_system_info(),
                'performance': self.get_performance_metrics(),
                'timestamp': datetime.now().isoformat()
            }

            response = requests.post(
                f"{self.server_url}/api/assets/status/",
                headers=self.headers,
                json=data
            )

            if response.status_code == 201:
                logger.info("Status reported successfully")
            else:
                logger.error(f"Failed to report status: {response.status_code} - {response.text}")

        except Exception as e:
            logger.error(f"Error reporting status: {str(e)}")

    def register_asset(self):
        """注册资产到服务器"""
        try:
            system_info = self.get_system_info()
            data = {
                'name': system_info['hostname'],
                'ip_address': system_info['ip_address'],
                'mac_address': system_info['mac_address'],
                'os_type': system_info['os_type'],
                'os_version': system_info['os_version'],
                'description': f"Platform: {system_info['platform']}\nProcessor: {system_info['processor']}"
            }

            response = requests.post(
                f"{self.server_url}/api/assets/assets/",
                headers=self.headers,
                json=data
            )

            if response.status_code in [201, 200]:
                logger.info("Asset registered successfully")
                return response.json()['id']
            else:
                logger.error(f"Failed to register asset: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error registering asset: {str(e)}")
            return None

    def run(self):
        """运行监控程序"""
        logger.info("Starting asset monitor...")
        asset_id = self.register_asset()
        
        if not asset_id:
            logger.error("Failed to register asset. Exiting...")
            return

        logger.info(f"Asset registered with ID: {asset_id}")

        while True:
            try:
                self.report_status()
                time.sleep(self.interval)
            except KeyboardInterrupt:
                logger.info("Stopping asset monitor...")
                break
            except Exception as e:
                logger.error(f"Unexpected error: {str(e)}")
                time.sleep(self.interval)

def main():
    """主函数"""
    import argparse
    parser = argparse.ArgumentParser(description='Asset Monitor Agent')
    parser.add_argument('--server', required=True, help='Server URL')
    parser.add_argument('--token', help='API Token')
    parser.add_argument('--interval', type=int, default=60, help='Reporting interval in seconds')
    
    args = parser.parse_args()
    
    monitor = AssetMonitor(args.server, args.token, args.interval)
    monitor.run()

if __name__ == '__main__':
    main()
