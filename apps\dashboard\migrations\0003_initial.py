# Generated by Django 5.1.3 on 2024-11-21 08:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('assets', '0002_initial'),
        ('dashboard', '0002_initial'),
        ('exercise', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='dashboardconfig',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_dashboard_configs', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddField(
            model_name='dashboardmetric',
            name='exercise',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dashboard_metrics', to='exercise.exercise', verbose_name='所属演练'),
        ),
        migrations.AddField(
            model_name='infectionrecord',
            name='asset',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dashboard_infection_records', to='assets.asset', verbose_name='感染资产'),
        ),
        migrations.AddField(
            model_name='infectionrecord',
            name='exercise',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dashboard_infection_records', to='exercise.exercise', verbose_name='所属演练'),
        ),
        migrations.AddField(
            model_name='infectiontrend',
            name='exercise',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='infection_trends', to='exercise.exercise'),
        ),
        migrations.AddField(
            model_name='phishingrecord',
            name='exercise',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dashboard_phishing_records', to='exercise.exercise', verbose_name='所属演练'),
        ),
        migrations.AddField(
            model_name='phishingrecord',
            name='user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dashboard_phishing_records', to=settings.AUTH_USER_MODEL, verbose_name='点击用户'),
        ),
        migrations.AddIndex(
            model_name='assetstatus',
            index=models.Index(fields=['asset', 'timestamp'], name='dashboard_a_asset_i_42bce0_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='infectionrecord',
            unique_together={('exercise', 'asset')},
        ),
        migrations.AddIndex(
            model_name='infectiontrend',
            index=models.Index(fields=['exercise', 'timestamp'], name='dashboard_i_exercis_1402b5_idx'),
        ),
    ]
