from django.db.models import Prefetch
from django.db.models import Q
from django.shortcuts import get_object_or_404
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, extend_schema_view, inline_serializer

from .models import Chat, Message
from .serializers import ChatSerializer, ChatMessageSerializer, MessageSerializer
from .utils import AIClient


@extend_schema_view(
    list=extend_schema(
        summary="获取聊天会话列表",
        description="获取所有聊天会话信息，包含最近5条消息",
        tags=["聊天管理"]
    ),
    create=extend_schema(
        summary="创建聊天会话",
        description="创建新的聊天会话",
        tags=["聊天管理"]
    ),
    retrieve=extend_schema(
        summary="获取聊天会话详情",
        description="获取指定聊天会话的详细信息",
        tags=["聊天管理"]
    ),
    destroy=extend_schema(
        summary="删除聊天会话",
        description="删除指定聊天会话(需要认证)",
        tags=["聊天管理"]
    ),
    send_message=extend_schema(
        summary="发送消息",
        description="向指定聊天会话发送消息并获取AI回复",
        tags=["聊天管理"],
        request=ChatMessageSerializer,
        responses={
            200: inline_serializer(
                name='SendMessageResponse',
                fields={
                    'message': MessageSerializer()
                }
            )
        }
    ),
    history=extend_schema(
        summary="获取聊天历史",
        description="获取指定聊天会话的所有历史消息",
        tags=["聊天管理"],
        responses={
            200: MessageSerializer(many=True)
        }
    )
)
class ChatViewSet(viewsets.ModelViewSet):
    """聊天会话视图集"""
    serializer_class = ChatSerializer
    http_method_names = ['get', 'post', 'delete']  # 只允许GET、POST和DELETE方法
    
    def get_permissions(self):
        """
        根据不同的action返回不同的权限要求
        - destroy: 需要认证
        - 其他: 允许所有人访问
        """
        if self.action == 'destroy':
            permission_classes = [IsAuthenticated]
        else:
            permission_classes = [AllowAny]
        return [permission() for permission in permission_classes]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.ai_client = AIClient()
    
    def get_queryset(self):
        # 移除用户过滤
        return Chat.objects.all().prefetch_related(
            Prefetch(
                'messages',
                queryset=Message.objects.order_by('-created_at')[:5],
                to_attr='recent_messages'
            )
        )
    
    def get_object(self):
        """
        重写get_object方法,同时支持通过id和conversation_id查找chat
        """
        queryset = self.get_queryset()
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        assert lookup_url_kwarg in self.kwargs, (
            'Expected view %s to be called with a URL keyword argument '
            'named "%s". Fix your URL conf, or set the `.lookup_field` '
            'attribute on the view correctly.' %
            (self.__class__.__name__, lookup_url_kwarg)
        )
        
        lookup_value = self.kwargs[lookup_url_kwarg]
        
        # 检查lookup_value是否有效
        if lookup_value == 'undefined' or not lookup_value:
            from rest_framework.exceptions import NotFound
            raise NotFound(detail="聊天会话不存在")
            
        try:
            # 先尝试通过id查找,如果找不到再通过conversation_id查找
            obj = get_object_or_404(queryset, Q(id=lookup_value) | Q(conversation_id=lookup_value))
            
            # 检查权限
            self.check_object_permissions(self.request, obj)
            
            return obj
        except ValueError:
            from rest_framework.exceptions import NotFound
            raise NotFound(detail="无效的会话ID")
    
    @action(detail=True, methods=['post'])
    def send_message(self, request, pk=None):
        """发送消息"""
        chat = self.get_object()
        enable_auto_reply = chat.negotiation.enable_auto_reply
        serializer = ChatMessageSerializer(data=request.data)

        if serializer.is_valid():
            # 保存用户消息
            user_message = Message.objects.create(
                chat=chat,
                role='user',
                content=serializer.validated_data['content']
            )
            if enable_auto_reply:

                # 直接发送当前消息给AI
                messages = [{
                    "role": "user",
                    "content": serializer.validated_data['content']
                }]

                # 调用AI获取回复,传入conversation_id
                assistant_reply = self.ai_client.get_chat_response(
                    messages,
                    user_id=str(chat.negotiation.n_id),
                    conversation_id=chat.conversation_id
                )

                # 保存助手回复
                assistant_message = Message.objects.create(
                    chat=chat,
                    role='assistant',
                    content=assistant_reply
                )

                # 返回序列化后的消息
                return Response({
                    'message': MessageSerializer(assistant_message).data
                })
            else:
                return Response({})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def artificial_message(self, request, pk=None):
        chat = self.get_object()
        serializer = ChatMessageSerializer(data=request.data)
        enable_auto_reply = chat.negotiation.enable_auto_reply
        if enable_auto_reply:
            return Response({"msg": "该会话不支持人工回复"}, status=status.HTTP_400_BAD_REQUEST)
        if serializer.is_valid():
            # 保存用户消息
            user_message = Message.objects.create(
                chat=chat,
                role='artificial',
                content=serializer.validated_data['content']
            )
            # 返回序列化后的消息
            return Response({
                'message': "发送成功"
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['get'])
    def history(self, request, pk=None):
        """获取聊天历史"""
        chat = self.get_object()
        messages = chat.messages.exclude(role='system').order_by("created_at")
        serializer = MessageSerializer(messages, many=True)
        return Response(serializer.data)
