from django5_aliyun_oss.storage import AliyunOSSStorage
from urllib.parse import urljoin
import os


class CustomAliyunOSSStorage(AliyunOSSStorage):

    def url(self, name):
        """
        生成文件的公共访问 URL（无签名）
        """
        # 标准化文件路径,将Windows风格的反斜杠转换为URL风格的正斜杠
        name = name.replace('\\', '/')
        
        # 构建文件的公共 URL
        endpoint = self.endpoint
        if endpoint.startswith('http://'):  # NOQA
            endpoint = endpoint[7:]
        elif endpoint.startswith('https://'):
            endpoint = endpoint[8:]

        # 构造无签名的 URL
        public_url = f'https://{self.bucket_name}.{endpoint}/{name}'
        return public_url

    def _save(self, name, content):
        """
        重写保存方法,确保文件路径使用正斜杠
        """
        # 标准化文件名
        name = name.replace('\\', '/')
        return super()._save(name, content)
