from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.users.models import User
from config.models import BaseModel
from django.core.exceptions import ValidationError


class Asset(BaseModel):
    """资产模型"""
    ASSET_TYPES = (
        ('EP', _('终端')),
        ('SV', _('服务器')),
        ('NW', _('网络设备')),
        ('EM', _('电子邮件')),
    )

    OS_TYPES = (
        ('WIN7', 'Windows 7'),
        ('WIN10', 'Windows 10'),
        ('WIN10_W', 'Windows 10网信版'),
        ('WIN11', 'Windows 11'),
        ('CENTOS_7', 'CentOS 7'),
        ('WIN_SERVER', 'Windows Server'),
        ('KALI_LINUX', 'kali_linux'),
        ('IOS', 'ios'),
        ('MACOS', 'MacOS'),
        ('OTHER', _('其他')),
    )

    name = models.CharField(_("设备名称"), max_length=100, blank=True, null=True)
    asset_type = models.CharField(_("资产类型"), max_length=2, choices=ASSET_TYPES)
    username = models.CharField(_("使用者(拥有人)"), max_length=100, blank=True)
    ip_address_v4 = models.GenericIPAddressField(_("IP V4地址"), blank=True, null=True)
    ip_address_v6 = models.GenericIPAddressField(_("IP V6地址"), blank=True, null=True)
    mac_address = models.CharField(_("MAC地址"), max_length=17, blank=True)
    os_type = models.CharField(_("操作系统"), max_length=20, choices=OS_TYPES, blank=True, null=True)
    os_version = models.CharField(_("系统版本"), max_length=50, blank=True)
    department = models.CharField(_("所属部门"), max_length=100, blank=True, null=True)
    group = models.ForeignKey("assets.AssetGroup", verbose_name=_("所属资产组"), related_name="asset",
                              on_delete=models.SET_NULL, null=True)
    email = models.CharField(_("邮箱"), max_length=200, blank=True, null=True)
    description = models.TextField(_("备注"), blank=True, null=True)
    is_active = models.BooleanField(_("是否在线"), default=True)
    last_seen = models.DateTimeField(_("最后在线时间"), auto_now=True, null=True, blank=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_assets",
        verbose_name="创建者",
    )

    class Meta:
        db_table = 'ls_asset'
        verbose_name = _("资产")
        verbose_name_plural = _("资产")

    def __str__(self):
        return f"{self.name} ({self.ip_address_v4})"

    @property
    def status(self):
        """获取资产当前状态"""
        from apps.infection.models import InfectionRecord
        latest_record = InfectionRecord.objects.filter(
            asset=self
        ).order_by('-created_at').first()

        if not latest_record:
            return 'normal'
        return latest_record.status


class AssetImportHistory(BaseModel):
    """资产导入历史"""
    IMPORT_STATUS = (
        ('PE', _('等待处理')),
        ('PR', _('处理中')),
        ('SU', _('成功')),
        ('FA', _('失败')),
    )

    file_name = models.CharField(_("文件名"), max_length=200)
    file_size = models.IntegerField(_("文件大小(bytes)"))
    total_count = models.IntegerField(_("总记录数"), default=0)
    success_count = models.IntegerField(_("成功数"), default=0)
    failed_count = models.IntegerField(_("失败数"), default=0)
    status = models.CharField(_("状态"), max_length=2,
                              choices=IMPORT_STATUS, default='PE')
    error_message = models.TextField(_("错误信息"), blank=True)
    completed_at = models.DateTimeField(_("完成时间"), null=True, blank=True)

    class Meta:
        verbose_name = _("资产导入历史")
        verbose_name_plural = _("资产导入历史")
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.file_name} - {self.get_status_display()}"


class AssetGroup(BaseModel):
    """资产组"""

    name = models.CharField("组名", max_length=100)
    description = models.TextField("描述", blank=True)
    # assets = models.ManyToManyField(
    #     Asset, related_name="groups", verbose_name="资产列表"
    # )
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_asset_groups",
        verbose_name="创建者",
    )

    class Meta:
        verbose_name = "资产组"
        verbose_name_plural = verbose_name
        ordering = ["name"]

    def __str__(self):
        return self.name
