from django.db import models
from config.models import BaseModel
from django.utils.translation import gettext_lazy as _
from apps.users.models import User


# Create your models here.


class MailHeaderModel(BaseModel):
    """
    自定义邮件头
    """
    x_custom_header = models.Char<PERSON>ield(verbose_name="X-Custom-Header", max_length=200)
    gophish = models.CharField(verbose_name="{{.URL}}-gophish", max_length=200)

    class Meta:
        db_table = 'ls_mail_header'
        verbose_name = _("自定义邮件头")
        verbose_name_plural = _("自定义邮件头")
        ordering = ['-id']


class StrategyModel(BaseModel):
    """
    发送邮件策略
    """
    API_TYPES = (
        ('SMTP', 'SMTP'),
        ('POP3', 'POP3'),
        ('IMAP', 'IMAP'),
        ('WEBMAIL', 'WEBMAIL'),

    )
    name = models.Cha<PERSON><PERSON><PERSON>(verbose_name="策略名称", max_length=100)
    api_type = models.Char<PERSON>ield(verbose_name="接口类型", max_length=10, choices=API_TYPES)
    sender_email = models.Char<PERSON><PERSON>(verbose_name="发件人邮箱", max_length=100)
    port = models.IntegerField(verbose_name="邮件端口", null=True, blank=True)
    email_server_address = models.CharField(verbose_name="邮箱服务器地址", max_length=100)
    email_server_account = models.CharField(verbose_name="邮箱服务器账号", max_length=100)
    email_server_pwd = models.CharField(verbose_name="邮箱服务器密码", max_length=100)
    is_ignore_certificate_errors = models.BooleanField(verbose_name="是否忽略证书错误", default=False)
    status = models.BooleanField(verbose_name="状态", default=True)
    # 自定义邮件头
    mail_headers = models.ManyToManyField("MailHeaderModel", verbose_name="自定义邮件头",
                                          db_table="ls_strategy_to_mail_header", blank=True)

    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="strategies",
        verbose_name="创建者",
    )

    class Meta:
        db_table = 'ls_strategy'
        verbose_name = _("发送邮件策略")
        verbose_name_plural = _("发送邮件策略")
        ordering = ['-id']

    def __str__(self):
        return f"{self.name}"


class EmailTemplateFileModel(BaseModel):
    """
    邮件模板附件
    """
    virus = models.ForeignKey(verbose_name="病毒", to="virus.Virus", on_delete=models.SET_NULL, null=True, blank=True)
    email_file = models.FileField(verbose_name="文件", upload_to="email_template/", blank=True, null=True)
    file_name = models.CharField(verbose_name="文件名称", max_length=200, null=True, blank=True)
    file_size = models.CharField(verbose_name="文件大小", max_length=200, null=True, blank=True)
    is_compress_attachments = models.BooleanField(verbose_name="是否压缩附件", default=False)
    zip_name = models.CharField(verbose_name="压缩包名称", max_length=100, null=True, blank=True)
    zip_password = models.CharField(verbose_name="压缩包密码", max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'ls_email_template_file'
        verbose_name = _("邮件模板附件")
        verbose_name_plural = _("邮件模板附件")
        ordering = ['-id']

    def __str__(self):
        return f"{self.virus.name}"


class EmailTemplateModel(BaseModel):
    """
    邮件模板
    """
    name = models.CharField(verbose_name="邮件模板名称", max_length=100)
    email_subject = models.CharField(verbose_name="邮件模板主题", max_length=100)
    # sender = models.CharField(verbose_name="邮件模板发件人", max_length=100)
    # email_server = models.CharField(verbose_name="邮件模板邮件服务器", max_length=100)
    content = models.TextField(verbose_name="邮件模板内容", null=True, blank=True)
    is_track_user = models.BooleanField(verbose_name="是否跟踪用户", default=False)
    is_redirect_url = models.BooleanField(verbose_name="自动跳转URL", default=False)
    # 自定义邮件头
    email_template_file = models.ManyToManyField("EmailTemplateFileModel", verbose_name="邮件模板附件",
                                                 db_table="ls_email_template_to_files", blank=True)

    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="email_templates",
        verbose_name="创建者",
    )

    class Meta:
        db_table = 'ls_email_template'
        verbose_name = _("邮件模板")
        verbose_name_plural = _("邮件模板")
        ordering = ['-id']

    def __str__(self):
        return f"{self.name}"


class EmailLog(BaseModel):
    """
    邮件发送记录
    """
    STATUS_CHOICES = (
        ('PENDING', '待发送'),
        ('SENDING', '发送中'),
        ('SUCCESS', '发送成功'),
        ('FAILED', '发送失败'),
        ('RETRY', '重试中'),
    )

    email_task = models.ForeignKey(
        "EmailTakModel", 
        verbose_name="邮件任务",
        on_delete=models.CASCADE,
        related_name="email_logs"
    )
    exercise = models.ForeignKey(
        "exercise.Exercise",
        verbose_name="演练任务",
        on_delete=models.CASCADE,
        related_name="email_logs",
        null=True,
        blank=True,
    )
    recipient = models.EmailField(verbose_name="收件人")
    status = models.CharField(
        verbose_name="发送状态",
        max_length=20,
        choices=STATUS_CHOICES,
        default='PENDING'
    )
    error_message = models.TextField(verbose_name="错误信息", null=True, blank=True)
    retry_count = models.IntegerField(verbose_name="重试次数", default=0)
    sent_at = models.DateTimeField(verbose_name="发送时间", null=True, blank=True)
    task_id = models.CharField(verbose_name="任务ID", max_length=100, null=True, blank=True)
    is_click = models.BooleanField(verbose_name="邮件是否被点击", default=False)
    user_flag = models.CharField(verbose_name="发送邮件用户标识", max_length=200, null=True, blank=True)

    class Meta:
        db_table = 'ls_email_log'
        verbose_name = _("邮件发送记录")
        verbose_name_plural = _("邮件发送记录")
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.email_task.name} - {self.recipient} - {self.status}"


class FormSubmitModel(BaseModel):
    """
    表单提交数据
    """
    STATUS_CHOICES = (
        ('CLICK', '点击访问'),
        ('SUBMIT', '提交表单数据')
    )
    email_log = models.ForeignKey(to="EmailLog", verbose_name="邮件记录", on_delete=models.CASCADE
                                  , related_name="form_submits")
    status = models.CharField(
        verbose_name="钓鱼状态",
        max_length=20,
        choices=STATUS_CHOICES,
        default='CLICK'
    )
    for_data = models.JSONField(_("表单提交信息"), default=dict, null=True, blank=True)
    browser_info = models.CharField(max_length=255, blank=True, null=True, verbose_name="浏览器信息")
    os_info = models.CharField(max_length=255, blank=True, null=True, verbose_name="操作系统信息")

    class Meta:
        db_table = 'ls_form_submit'
        verbose_name = _("表单提交数据")
        verbose_name_plural = _("表单提交数据")
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.email_log.exercise}"


class PhishingPageModel(BaseModel):
    """
    钓鱼页面
    """
    name = models.CharField(verbose_name="页面名称", max_length=200)
    redirect_address = models.CharField(verbose_name="重定向地址", max_length=300, null=True, blank=True)
    request_url = models.CharField(verbose_name="克隆网站", max_length=300, null=True, blank=True)
    content = models.TextField(verbose_name="内容", null=True, blank=True)
    is_capture_submitted_data = models.BooleanField(verbose_name="捕获提交数据", default=False)
    is_capture_password = models.BooleanField(verbose_name="捕获密码", default=False)

    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="phishing_pages",
        verbose_name="创建者",
    )

    class Meta:
        db_table = 'ls_phishing_page'
        verbose_name = _("钓鱼页面")
        verbose_name_plural = _("钓鱼页面")
        ordering = ['-id']

    def __str__(self):
        return self.name


class EmailTakModel(BaseModel):
    """
    邮件任务
    """
    name = models.CharField(verbose_name="任务名称", max_length=200)
    email_template = models.ForeignKey(to="phishing.EmailTemplateModel", verbose_name="邮件模板",
                                       on_delete=models.SET_NULL,
                                       related_name="email_task", null=True, blank=True)

    phishing_page = models.ForeignKey(to="phishing.PhishingPageModel", verbose_name="钓鱼页面",
                                      on_delete=models.SET_NULL,
                                      related_name="email_task", null=True, blank=True)

    phishing_link = models.CharField(verbose_name="钓鱼链接", max_length=300, null=True, blank=True)
    strategy = models.ForeignKey(to="phishing.StrategyModel", verbose_name="发送策略",
                                 on_delete=models.SET_NULL,
                                 related_name="email_task", null=True, blank=True)

    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="email_task",
        verbose_name="创建者",
    )

    class Meta:
        db_table = 'ls_email_task'
        verbose_name = _("邮件任务")
        verbose_name_plural = _("邮件任务")
        ordering = ['-id']

    def __str__(self):
        return self.name
