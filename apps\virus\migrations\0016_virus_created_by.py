# Generated by Django 5.1.3 on 2025-02-10 16:55

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('virus', '0015_remove_virusevolutionmodel_resource_control_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='virus',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='virus', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
    ]
