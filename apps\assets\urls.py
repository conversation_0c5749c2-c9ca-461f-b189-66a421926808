from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from .views import (
    AssetViewSet, AssetGroupViewSet,
    AssetImportHistoryViewSet, ExtensionsAssetView
)

router = DefaultRouter()
router.register(r'assets', AssetViewSet, basename="assets")
router.register(r'groups', AssetGroupViewSet)
router.register(r'import-history', AssetImportHistoryViewSet)
router.register(r"exercise/(?P<exercise_id>\d+)/assets", ExtensionsAssetView,
                basename="extensions_assets")

urlpatterns = [
    path('', include(router.urls)),
]
