# Generated by Django 5.1.3 on 2024-12-21 15:56

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0004_assetstatus_created_at_assetstatus_updated_at_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='assetstatus',
            name='asset',
        ),
        migrations.RemoveField(
            model_name='dashboardconfig',
            name='created_by',
        ),
        migrations.AlterUniqueTogether(
            name='infectionrecord',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='infectionrecord',
            name='asset',
        ),
        migrations.RemoveField(
            model_name='infectionrecord',
            name='exercise',
        ),
        migrations.RemoveField(
            model_name='infectiontrend',
            name='exercise',
        ),
        migrations.RemoveField(
            model_name='phishingrecord',
            name='exercise',
        ),
        migrations.RemoveField(
            model_name='phishingrecord',
            name='user',
        ),
        migrations.RemoveField(
            model_name='dashboardmetric',
            name='encrypted_count',
        ),
        migrations.RemoveField(
            model_name='dashboardmetric',
            name='endpoint_infected',
        ),
        migrations.RemoveField(
            model_name='dashboardmetric',
            name='exercise',
        ),
        migrations.RemoveField(
            model_name='dashboardmetric',
            name='server_infected',
        ),
        migrations.RemoveField(
            model_name='dashboardmetric',
            name='total_assets',
        ),
        migrations.RemoveField(
            model_name='dashboardmetric',
            name='total_data_loss',
        ),
        migrations.DeleteModel(
            name='AlertHistory',
        ),
        migrations.DeleteModel(
            name='AssetStatus',
        ),
        migrations.DeleteModel(
            name='DashboardConfig',
        ),
        migrations.DeleteModel(
            name='InfectionRecord',
        ),
        migrations.DeleteModel(
            name='InfectionTrend',
        ),
        migrations.DeleteModel(
            name='PhishingRecord',
        ),
    ]
