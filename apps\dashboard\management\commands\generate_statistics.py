from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Count, Sum, Avg, Q
from datetime import datetime, timedelta
import json
import os
from apps.exercise.models import Exercise
from apps.infection.models import InfectionRecord, DataLeakage
from apps.assets.models import Asset
from django.conf import settings

class Command(BaseCommand):
    help = '生成演练统计报表'

    def add_arguments(self, parser):
        parser.add_argument(
            '--period',
            choices=['daily', 'weekly', 'monthly'],
            default='daily',
            help='统计周期（每日/每周/每月）'
        )
        parser.add_argument(
            '--output',
            help='输出文件路径'
        )

    def handle(self, *args, **options):
        period = options['period']
        
        # 确定统计时间范围
        end_time = timezone.now()
        if period == 'daily':
            start_time = end_time - timedelta(days=1)
            period_name = end_time.strftime('%Y%m%d')
        elif period == 'weekly':
            start_time = end_time - timedelta(days=7)
            period_name = f"{end_time.strftime('%Y%m%d')}_weekly"
        else:  # monthly
            start_time = end_time - timedelta(days=30)
            period_name = f"{end_time.strftime('%Y%m')}_monthly"
        
        try:
            # 收集统计数据
            stats_data = self.collect_statistics(start_time, end_time)
            
            # 确定输出路径
            if options['output']:
                output_path = options['output']
            else:
                filename = f"exercise_statistics_{period_name}.json"
                output_path = os.path.join(settings.MEDIA_ROOT, 'statistics', filename)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 写入统计数据
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(stats_data, f, ensure_ascii=False, indent=2)
            
            self.stdout.write(
                self.style.SUCCESS(f'统计报表已生成: {output_path}')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'生成统计报表时出错: {str(e)}')
            )

    def collect_statistics(self, start_time, end_time):
        """收集统计数据"""
        # 演练统计
        exercise_stats = Exercise.objects.filter(
            created_at__range=(start_time, end_time)
        ).aggregate(
            total_exercises=Count('id'),
            completed_exercises=Count('id', filter=Q(status__in=['FI', 'TE'])),
            active_exercises=Count('id', filter=Q(status__in=['RU', 'PA']))
        )
        
        # 感染统计
        infection_stats = InfectionRecord.objects.filter(
            infection_time__range=(start_time, end_time)
        ).aggregate(
            total_infections=Count('id'),
            total_encrypted=Count('id', filter=Q(status='EN')),
            total_data_loss=Sum('data_loss'),
            avg_encrypted_files=Avg('encrypted_files')
        )
        
        # 资产类型分布
        asset_type_stats = InfectionRecord.objects.filter(
            infection_time__range=(start_time, end_time)
        ).values(
            'asset__asset_type'
        ).annotate(
            count=Count('id')
        ).order_by('asset__asset_type')
        
        # 部门分布
        department_stats = InfectionRecord.objects.filter(
            infection_time__range=(start_time, end_time)
        ).values(
            'asset__department'
        ).annotate(
            count=Count('id')
        ).order_by('-count')
        
        # 数据泄露统计
        leakage_stats = DataLeakage.objects.filter(
            detected_time__range=(start_time, end_time)
        ).aggregate(
            total_leakages=Count('id'),
            total_files=Sum('file_count'),
            total_size=Sum('data_size')
        )
        
        # 敏感度分布
        sensitivity_stats = DataLeakage.objects.filter(
            detected_time__range=(start_time, end_time)
        ).values(
            'sensitivity'
        ).annotate(
            count=Count('id')
        ).order_by('sensitivity')
        
        # 计算总体指标
        total_assets = Asset.objects.count()
        infected_assets = InfectionRecord.objects.filter(
            infection_time__range=(start_time, end_time)
        ).values('asset').distinct().count()
        
        overall_stats = {
            'time_range': {
                'start': start_time.isoformat(),
                'end': end_time.isoformat()
            },
            'overall': {
                'total_assets': total_assets,
                'infected_assets': infected_assets,
                'infection_rate': round(infected_assets / total_assets * 100, 2) if total_assets > 0 else 0
            },
            'exercises': exercise_stats,
            'infections': infection_stats,
            'asset_types': {
                item['asset__asset_type']: item['count']
                for item in asset_type_stats
            },
            'departments': {
                item['asset__department']: item['count']
                for item in department_stats
            },
            'data_leakage': {
                **leakage_stats,
                'sensitivity_distribution': {
                    item['sensitivity']: item['count']
                    for item in sensitivity_stats
                }
            }
        }
        
        return overall_stats 