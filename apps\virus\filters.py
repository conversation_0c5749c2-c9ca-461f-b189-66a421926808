from django_filters import rest_framework as filters
from .models import Virus
from django.db import models


class VirusFilter(filters.FilterSet):
    """病毒过滤器"""
    search = filters.CharFilter(method='filter_search', label="搜索")
    infection = filters.CharFilter(field_name='infection', lookup_expr='exact', label="感染方式")

    class Meta:
        model = Virus
        fields = ['search', 'infection']

    def filter_search(self, queryset, name, value):
        """
        自定义搜索方法，用于搜索 name 字段
        """
        return queryset.filter(
            models.Q(name__icontains=value)
        )

