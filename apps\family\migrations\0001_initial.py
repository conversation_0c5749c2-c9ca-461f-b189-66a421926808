# Generated by Django 5.1.3 on 2024-12-20 14:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='VirusFamily',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, verbose_name='病毒家族名称')),
                ('logo', models.ImageField(upload_to='virus_family_logo/', verbose_name='Logo')),
                ('first_seen_time', models.DateField(verbose_name='首次出现时间')),
                ('encryption_algorithm', models.CharField(choices=[('AES', 'AES'), ('RSA', 'RSA'), ('CHA', 'CHACHA20'), ('OTHER', '其他')], max_length=10, verbose_name='加密算法')),
                ('ransom_method', models.CharField(choices=[('SINGLE', '单重加密'), ('DOUBLE', '双重加密'), ('TRIPLE', '三重加密'), ('QUADRUPLE', '四重加密')], max_length=10, verbose_name='勒索方式')),
                ('infection_type', models.CharField(choices=[('WINDOWS', 'WINDOWS'), ('LINUX', 'LINUX'), ('NAS', 'NAS')], max_length=10, verbose_name='感染类型')),
                ('wallet_address', models.CharField(max_length=100, verbose_name='钱包地址')),
                ('public_decrypt', models.BooleanField(default=False, verbose_name='公开解密器')),
                ('encrypted_suffix', models.CharField(max_length=50, verbose_name='加密后缀')),
                ('description', models.TextField(verbose_name='家族简介')),
                ('attack_vector', models.TextField(verbose_name='攻击路线')),
                ('ioc', models.TextField(verbose_name='IOC信息')),
            ],
            options={
                'verbose_name': '病毒家族',
                'verbose_name_plural': '病毒家族',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Victim',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('date', models.DateField(verbose_name='日期')),
                ('victim', models.CharField(max_length=100, verbose_name='受害者')),
                ('official_website', models.URLField(verbose_name='官网')),
                ('location', models.CharField(max_length=100, verbose_name='所在地区')),
                ('introduction', models.TextField(verbose_name='简介')),
                ('virus_family', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='victims', to='family.virusfamily', verbose_name='病毒家族')),
            ],
            options={
                'verbose_name': '受害者',
                'verbose_name_plural': '受害者',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='Tool',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, verbose_name='工具名称')),
                ('tool_type', models.CharField(choices=[('LM', '内网横向'), ('SCAN', '内网扫描')], max_length=10, verbose_name='工具类型')),
                ('introduction', models.TextField(verbose_name='工具介绍')),
                ('file', models.FileField(upload_to='virus_family_tools/', verbose_name='工具文件')),
                ('virus_family', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tools', to='family.virusfamily', verbose_name='病毒家族')),
            ],
            options={
                'verbose_name': '工具',
                'verbose_name_plural': '工具',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RansomNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, verbose_name='勒索信名称')),
                ('content', models.TextField(verbose_name='文本内容')),
                ('virus_family', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ransom_notes', to='family.virusfamily', verbose_name='病毒家族')),
            ],
            options={
                'verbose_name': '勒索信',
                'verbose_name_plural': '勒索信',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RansomAddress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('address_option', models.CharField(choices=[('TOR', 'TOR网络'), ('TELEGRAM', 'Telegram'), ('EMAIL', '电子邮件')], max_length=10, verbose_name='地址选项')),
                ('content', models.TextField(verbose_name='文本内容')),
                ('virus_family', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ransom_addresses', to='family.virusfamily', verbose_name='病毒家族')),
            ],
            options={
                'verbose_name': '勒索地址',
                'verbose_name_plural': '勒索地址',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Negotiation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('date', models.DateField(verbose_name='日期')),
                ('initial_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='初始金额')),
                ('final_delivery_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='最终交付金额')),
                ('is_pay', models.BooleanField(default=False, verbose_name='是否支付')),
                ('file', models.FileField(upload_to='negotiation_record/', verbose_name='聊天记录')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('virus_family', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='negotiations', to='family.virusfamily', verbose_name='病毒家族')),
            ],
            options={
                'verbose_name': '谈判记录',
                'verbose_name_plural': '谈判记录',
                'ordering': ['-date'],
            },
        ),
    ]
