from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import SystemSettingsViewSet, SystemConfigViewSet, UploadViewSet, Notifications

# 创建路由器
router = DefaultRouter()
router.register(r'system/settings', SystemSettingsViewSet, basename='system-settings')
router.register(r'system/configs', SystemConfigViewSet, basename='system-configs')
router.register(r'system/upload', UploadViewSet, basename='system-upload')
router.register(r'notifications', Notifications, basename='notifications')

# 定义URL模式
urlpatterns = router.urls
