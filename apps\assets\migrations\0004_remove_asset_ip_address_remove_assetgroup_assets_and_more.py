# Generated by Django 5.1.3 on 2024-12-04 06:06

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assets', '0003_assetimporthistory_updated_at_assetstatus_created_at_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='asset',
            name='ip_address',
        ),
        migrations.RemoveField(
            model_name='assetgroup',
            name='assets',
        ),
        migrations.AddField(
            model_name='asset',
            name='email',
            field=models.CharField(blank=True, max_length=200, verbose_name='邮箱'),
        ),
        migrations.AddField(
            model_name='asset',
            name='group',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='assets.assetgroup', verbose_name='所属资产组'),
        ),
        migrations.AddField(
            model_name='asset',
            name='ip_address_v4',
            field=models.GenericIPAddressField(blank=True, null=True, verbose_name='IP V4地址'),
        ),
        migrations.AddField(
            model_name='asset',
            name='ip_address_v6',
            field=models.GenericIPAddressField(blank=True, null=True, verbose_name='IP V6地址'),
        ),
        migrations.AddField(
            model_name='asset',
            name='username',
            field=models.CharField(blank=True, max_length=100, verbose_name='使用者(拥有人)'),
        ),
        migrations.AlterField(
            model_name='asset',
            name='asset_type',
            field=models.CharField(choices=[('EP', '终端'), ('SV', '服务器'), ('NW', '网络设备'), ('EM', '电子邮件')], max_length=2, verbose_name='资产类型'),
        ),
        migrations.AlterField(
            model_name='asset',
            name='department',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='所属部门'),
        ),
        migrations.AlterField(
            model_name='asset',
            name='description',
            field=models.TextField(blank=True, verbose_name='备注'),
        ),
        migrations.AlterField(
            model_name='asset',
            name='name',
            field=models.CharField(max_length=100, verbose_name='设备名称'),
        ),
    ]
