# 上传文件所在路径
import os
import platform

UPLOAD_FILE_PATH_DIC = {
    'email_template': 'email_template',  # 邮件模板
    'virus_family_tools': 'virus_family_tools',  # 勒索家族工具附件
    'negotiation_record': 'negotiation_record',  # 聊天记录
    'virus_family_logo': 'virus_family_logo',  # 病毒家族logo
    'encryptor': 'encryptor',  # 加密器
    'wallpaper': 'wallpaper',  # 壁纸
    'avatar': 'avatar',  # 头像
    'company_logo': 'company_logo',  # 公司logo
}

# 资产类型
Asset_TYPE_EP = 'EP'  # 终端
Asset_TYPE_SV = 'SV'  # 服务器
Asset_TYPE_NW = 'NW'  # 网络设备
Asset_TYPE_EM = 'EM'  # 电子邮件

# 资产操作系统内容与key对应关系
ASSET_OS_TYPE = {
    "Windows7": "WIN7",
    "Windows10": "WIN10",
    "Windows10网信版": "WIN10_W",
    "Windows11": "WIN11",
    "CentOS7": "CENTOS_7",
    "WindowsServer": "WIN_SERVER",
    "kali_linux": "KALI_LINUX",
    "ios": "IOS",
    "MacOS": "MACOS",
    "其他": "OTHER",
}

ASSET_OS_TO_TYPE = {
    "WIN7": "Windows7",
    "WIN10": "Windows10",
    "WIN10_W": "Windows10网信版",
    "WIN11": "Windows11",
    "CENTOS_7": "CentOS7",
    "WIN_SERVER": "WindowsServer",
    "KALI_LINUX": "kali_linux",
    "IOS": "ios",
    "MACOS": "MacOS",
    "OTHER": "其他",
}

# 钓鱼表单地址
PHISHING_LINK = os.getenv("PHISHING_LINK", "")
# 邮件发送模板隐藏链接构造
EMAIL_IMAGE_URL = f'<img src="{os.environ.get("RESERVE_EMAIL")}?email=%s&exercise_id=%s" width="1" height="1">'


# 访问钓鱼邮件状态
FORM_STATUS_CLICK = 'CLICK'  # 点击链接
FORM_STATUS_SUBMIT = 'SUBMIT'  # 提交表单

# 病毒地址
ENCRYPTOR_FILE_URL = "https://ransomware-emergency.oss-cn-qingdao.aliyuncs.com/virus_family_samples/20250523180144_Client_V2.1.exe"


# 导出演练报告HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>网络安全演练报告</title>
</head>

<body style="margin: 50px;">
  <table border="1" style="width: 100%; min-width: 900px; border-collapse: collapse;">
    <thead>
      <tr>
        <th style="text-align: center; height: 80px; font-size: 20px;" colspan="4">网络安全演练报告</th>
      </tr>
    </thead>

    <tbody>
      <tr>
        <td style="text-align: center; width: 150px; height: 64px;">演练名称</td>
        <td style="text-align: center; width: 300px; height: 64px;">{{ name }}</td>
        <td style="text-align: center; width: 150px; height: 64px;">演练时间</td>
        <td style="text-align: center; width: 300px; height: 64px;">{{ drill_time }}</td>
      </tr>

      <tr>
        <td style="text-align: center; height: 64px;">持续时间</td>
        <td style="text-align: center; height: 64px;">{{ duration }}</td>
        <td style="text-align: center; height: 64px;">演练单位</td>
        <td style="text-align: center; height: 64px;">{{ company_name }}</td>
      </tr>

      <tr>
        <td style="text-align: center; height: 64px;">演练状态</td>
        <td style="text-align: center; height: 64px;">{{ status }}</td>
        <td style="text-align: center; height: 64px;">病毒名称</td>
        <td style="text-align: center; height: 64px;">{{virus_name}}</td>
      </tr>

      <tr>
        <td style="text-align: center; height: 64px;">目标设备总数</td>
        <td style="text-align: center; height: 64px;">{{ devices_count }}</td>
        <td style="text-align: center; height: 64px;">已感染设备数</td>
        <td style="text-align: center; height: 64px;">{{ infected_devices }}</td>
      </tr>

      <tr>
        <td style="text-align: center; height: 64px;">发送总数</td>
        <td style="text-align: center; height: 64px;">{{ email_sent }}</td>
        <td style="text-align: center; height: 64px;">已点击数</td>
        <td style="text-align: center; height: 64px;">{{ email_clicked }}</td>
      </tr>

      <tr>
        <td style="text-align: center; height: 64px;">钓鱼邮件点击率</td>
        <td style="text-align: center; height: 64px;">{{ email_rate }}</td>
        <td></td>
        <td></td>
      </tr>
    </tbody>
  </table>

<table border="1" style="width: 100%; min-width: 900px; border-collapse: collapse; margin: 0 auto; border-top: none;">
  <thead>
    <tr>
      <th style="text-align: center; height: 64px;" colspan="6">捕获数据</th>
    </tr>
    <tr>
      <th style="text-align: center; height: 64px;">Email</th>
      <th style="text-align: center; height: 64px;">发送时间</th>
      <th style="text-align: center; height: 64px;">点击时间</th>
      <th style="text-align: center; height: 64px;">提交时间</th>
      <th style="text-align: center; height: 64px;">设备信息</th>
      <th style="text-align: center; height: 64px;">状态</th>
    </tr>
  </thead>
  <tbody>
    {% for item in capture_data %}
    <tr>
      <td style="text-align: center; height: 64px;">{{ item.email }}</td>
      <td style="text-align: center; height: 64px;">{{ item.send_email_time }}</td>
      <td style="text-align: center; height: 64px;">{{ item.click_time }}</td>
      <td style="text-align: center; height: 64px;">{{ item.submit_time }}</td>
      <td style="text-align: center; height: 64px;">{{ item.device_info }}</td>
      <td style="text-align: center; height: 64px;">{{ item.status }}</td>
    </tr>

    {% for t in item.timeline if t.status == 'SUBMIT' and t.data %}
    <tr>
      <td style="width: 100%; text-align: center; padding: 10px;" colspan="6">
        <table border="1" style="width: 100%; border-collapse: collapse; margin: 0 auto;">
          <tr>
            <td style="width: 50%; text-align: center; height: 48px; line-height: 48px; font-weight: bold;">参数名</td>
            <td style="width: 50%; text-align: center; height: 48px; line-height: 48px; font-weight: bold;">参数值</td>
          </tr>
          {% for key, value in t.data.items() %}
          <tr>
            <td style="text-align: center; height: 48px;">{{ key }}</td>
            <td style="text-align: center; height: 48px;">{{ value }}</td>
          </tr>
          {% endfor %}
        </table>
      </td>
    </tr>
    {% endfor %}

    {% endfor %}
  </tbody>
</table>

  <table border="1" style="width: 100%; min-width: 900px; border-collapse: collapse; margin: 0 auto; border-top: none;">
    <thead>
      <tr>
        <th style="text-align: center; height: 64px;" colspan="6">目标资产信息</th>
      </tr>
      <tr>
        <th style="text-align: center; height: 64px;">所属组</th>
        <th style="text-align: center; height: 64px;">资产名称</th>
        <th style="text-align: center; height: 64px;">资产类型</th>
        <th style="text-align: center; height: 64px;">状态</th>
        <th style="text-align: center; height: 64px;">IP地址</th>
      </tr>
    </thead>
    <tbody>
    {% for item in assets %}
      <tr>
        <td style="text-align: center; height: 64px;">{{ item.group }}</td>
        <td style="text-align: center; height: 64px;">{{ item.name }}</td>
        <td style="text-align: center; height: 64px;">{{ item.asset_type }}</td>
        <td style="text-align: center; height: 64px;">{{ item.is_infection }}</td>
        <td style="text-align: center; height: 64px;">{{ item.ip_address }}</td>
      </tr>
    {% endfor %}
    </tbody>
  </table>

  <table border="1" style="width: 100%; min-width: 900px; border-collapse: collapse; margin: 0 auto; border-top: none;">
    <thead>
      <tr>
        <th style="text-align: center; height: 64px;" colspan="6">邮件发送记录</th>
      </tr>
      <tr>
        <th style="text-align: center; height: 64px;">收件人</th>
        <th style="text-align: center; height: 64px;">邮件主题</th>
        <th style="text-align: center; height: 64px;">发送时间</th>
        <th style="text-align: center; height: 64px;">点击状态</th>
      </tr>
    </thead>
    <tbody>
        {% for item in mail_logs %}
      <tr>
        <td style="text-align: center; height: 64px;">{{ item.recipient }}</td>
        <td style="text-align: center; height: 64px;">{{ item.email_subject }}</td>
        <td style="text-align: center; height: 64px;">{{ item.send_time }}</td>
        <td style="text-align: center; height: 64px;">{{ item.is_click }}</td>
      </tr>
          {% endfor %}
    </tbody>
  </table>
</body>

</html>
"""


WKHTMLTOPDF_PATH = (
    r"D:\wkhtmltopdf\bin\wkhtmltopdf.exe"
    if platform.system() == 'Windows'
    else '/usr/bin/wkhtmltopdf'
)
