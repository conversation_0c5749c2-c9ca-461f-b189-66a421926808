from rest_framework import serializers


class NoValidationFileField(serializers.FileField):
    """
    自定义 FileField，跳过文件验证
    """

    def to_internal_value(self, data):
        # 如果传入的数据是字符串，直接返回它（假设它是文件路径或文件名）
        if isinstance(data, str):
            return data  # 如果传入的是文件路径，直接返回
        # 调用父类方法处理其他情况（如果有文件上传，则会处理）
        return super().to_internal_value(data)

    def to_representation(self, value):
        # 将返回值转化为文件的 URL 或路径
        if isinstance(value, str):
            # 如果是字符串类型，直接返回该值（假设是文件路径或 URL）
            return value
        # 否则使用父类的处理方式
        return super().to_representation(value)
