import os
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction

User = get_user_model()


class Command(BaseCommand):
    help = '创建默认的超级管理员账号'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            help='管理员用户名（可选，默认从环境变量ADMIN_USERNAME读取）',
        )
        parser.add_argument(
            '--email',
            type=str,
            help='管理员邮箱（可选，默认从环境变量ADMIN_EMAIL读取）',
        )
        parser.add_argument(
            '--password',
            type=str,
            help='管理员密码（可选，默认从环境变量ADMIN_PASSWORD读取）',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制创建，即使已存在超级管理员',
        )

    def handle(self, *args, **options):
        # 从命令行参数或环境变量获取配置
        username = options.get('username') or os.getenv('ADMIN_USERNAME', 'admin')
        email = options.get('email') or os.getenv('ADMIN_EMAIL', '<EMAIL>')
        password = options.get('password') or os.getenv('ADMIN_PASSWORD', 'admin123456')

        # 检查是否已存在超级管理员
        if not options.get('force'):
            existing_superusers = User.objects.filter(is_superuser=True)
            if existing_superusers.exists():
                self.stdout.write(
                    self.style.WARNING(
                        f'已存在 {existing_superusers.count()} 个超级管理员账号，跳过创建。'
                        f'如需强制创建，请使用 --force 参数。'
                    )
                )
                return

        # 检查用户名是否已存在
        if User.objects.filter(username=username).exists():
            if not options.get('force'):
                self.stdout.write(
                    self.style.ERROR(
                        f'用户名 "{username}" 已存在。如需强制创建，请使用 --force 参数。'
                    )
                )
                return
            else:
                # 强制模式下，删除现有用户
                User.objects.filter(username=username).delete()
                self.stdout.write(
                    self.style.WARNING(f'已删除现有用户 "{username}"')
                )

        try:
            with transaction.atomic():
                # 创建超级管理员
                user = User.objects.create_superuser(
                    username=username,
                    email=email,
                    password=password
                )
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'成功创建超级管理员账号:\n'
                        f'  用户名: {username}\n'
                        f'  邮箱: {email}\n'
                        f'  密码: {password}\n'
                        f'  用户ID: {user.id}'
                    )
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'创建超级管理员失败: {str(e)}')
            )
            raise
