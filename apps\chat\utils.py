import os
import time
from typing import List, Dict, Any
from django.conf import settings
from cozepy import Coze, TokenAuth, JWTAuth, JWTOAuthApp, Message, ChatStatus, COZE_CN_BASE_URL

class AIClient:
    """AI客户端封装类"""

    def __init__(self):
        """初始化AI客户端"""
        self.bot_id = settings.AI_CONFIG["bot_id"]
        self.auth_type = settings.AI_CONFIG.get("auth_type", "token")

        # 初始化认证相关参数
        if self.auth_type == "jwt":
            # 保存JWT认证相关参数
            self.jwt_client_id = settings.AI_CONFIG["jwt_client_id"]
            self.jwt_private_key = settings.AI_CONFIG["jwt_private_key"]
            self.jwt_public_key_id = settings.AI_CONFIG["jwt_public_key_id"]
            # 初始化JWTOAuthApp
            self.jwt_oauth_app = JWTOAuthApp(
                client_id=self.jwt_client_id,
                private_key=self.jwt_private_key,
                public_key_id=self.jwt_public_key_id,
                base_url=COZE_CN_BASE_URL
            )
            # 初始化Coze客户端
            self.coze = Coze(
                auth=JWTAuth(oauth_app=self.jwt_oauth_app),
                base_url=COZE_CN_BASE_URL
            )
        else:
            # 使用个人访问令牌认证
            self.access_token = settings.AI_CONFIG["access_token"]
            # 初始化Coze客户端
            self.coze = Coze(
                auth=TokenAuth(token=self.access_token),
                base_url=COZE_CN_BASE_URL
            )

        # 配置
        self.timeout = 600  # 10分钟超时
        self.poll_interval = 1  # 轮询间隔1秒

    def _refresh_jwt_token(self):
        """刷新JWT令牌

        当使用JWT认证时，每次API调用前都应该刷新令牌
        """
        if self.auth_type == "jwt":
            # 重新初始化Coze客户端以获取新的令牌
            self.coze = Coze(
                auth=JWTAuth(oauth_app=self.jwt_oauth_app),
                base_url=COZE_CN_BASE_URL
            )

    # 创建聊天会话
    def create_chat(self):
        # 刷新JWT令牌
        self._refresh_jwt_token()
        return self.coze.conversations.create(bot_id=self.bot_id)

    def get_chat_response(self, messages: List[Dict[str, str]], user_id: str, conversation_id: str = None) -> str:
        """获取AI回复

        Args:
            messages: 消息列表,每条消息包含role和content
            user_id: 用户ID
            conversation_id: 会话ID,用于保持会话连续性

        Returns:
            str: AI的回复内容

        Raises:
            Exception: 请求失败时抛出异常
        """
        try:
            # 刷新JWT令牌
            self._refresh_jwt_token()

            # 转换消息格式
            coze_messages = []
            for msg in messages:
                if msg["role"] == "user":
                    coze_messages.append(
                        Message.build_user_question_text(msg["content"])
                    )
                elif msg["role"] == "assistant":
                    coze_messages.append(
                        Message.build_assistant_answer(msg["content"])
                    )

            # 创建聊天并等待完成
            chat_poll = self.coze.chat.create_and_poll(
                bot_id=self.bot_id,
                user_id=user_id,
                conversation_id=conversation_id,  # 传入会话ID
                additional_messages=coze_messages
            )

            # 检查聊天状态
            if chat_poll.chat.status == ChatStatus.COMPLETED:
                # 返回第一条消息的内容
                return chat_poll.messages[0].content

            raise Exception("聊天未完成")

        except Exception as e:
            raise Exception(f"获取AI回复失败: {str(e)}")
