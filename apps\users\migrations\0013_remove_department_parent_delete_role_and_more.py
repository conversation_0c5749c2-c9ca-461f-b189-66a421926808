# Generated by Django 5.1.3 on 2024-12-31 14:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0012_alter_user_avatar'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='department',
            name='parent',
        ),
        migrations.DeleteModel(
            name='Role',
        ),
        migrations.RemoveField(
            model_name='user',
            name='department',
        ),
        migrations.RemoveField(
            model_name='user',
            name='role',
        ),
        migrations.AlterField(
            model_name='user',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active'),
        ),
        migrations.DeleteModel(
            name='Department',
        ),
    ]
