# Generated by Django 5.1.3 on 2024-11-26 02:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0004_department_updated_at_mfadevice_updated_at_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="SMSLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("phone", models.Char<PERSON>ield(max_length=11, verbose_name="手机号")),
                ("code", models.CharField(max_length=6, verbose_name="验证码")),
                (
                    "template_code",
                    models.CharField(max_length=20, verbose_name="模板编号"),
                ),
                ("status", models.Char<PERSON><PERSON>(max_length=10, verbose_name="发送状态")),
                (
                    "message",
                    models.CharField(
                        blank=True, max_length=200, null=True, verbose_name="状态信息"
                    ),
                ),
                (
                    "request_id",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="请求ID"
                    ),
                ),
                (
                    "biz_id",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="业务ID"
                    ),
                ),
            ],
            options={
                "verbose_name": "短信记录",
                "verbose_name_plural": "短信记录",
                "ordering": ["-created_at"],
            },
        ),
    ]
