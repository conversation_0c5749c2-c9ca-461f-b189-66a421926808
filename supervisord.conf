[unix_http_server]
file=/tmp/supervisor.sock
chmod=0700

; 启用网络HTTP服务器用于Web监控
[inet_http_server]
port=0.0.0.0:9002          ; 监听所有接口的9002端口
username=admin             ; 设置用户名
password=Admin@123456      ; 设置强密码

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisord]
nodaemon=true
logfile=/dev/stdout
logfile_maxbytes=0
pidfile=/tmp/supervisord.pid

[program:django]
command=gunicorn config.wsgi:application --bind 0.0.0.0:8001 --workers 4 --timeout 60 --keep-alive 5 --max-requests 1000 --max-requests-jitter 50
directory=/app
user=appuser
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/django.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5
stderr_logfile=/app/logs/django_error.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=5
stopasgroup=true
killasgroup=true

[program:qcluster]
command=python manage.py qcluster
directory=/app
user=appuser
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/qcluster.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5
stderr_logfile=/app/logs/qcluster_error.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=5
stopasgroup=true
killasgroup=true

[supervisorctl]
serverurl=http://127.0.0.1:9002
username=admin
password=Admin@123456