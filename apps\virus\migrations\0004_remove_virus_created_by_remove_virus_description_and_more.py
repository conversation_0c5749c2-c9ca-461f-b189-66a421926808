# Generated by Django 5.1.3 on 2024-12-21 11:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('family', '0001_initial'),
        ('virus', '0003_virus_ransom_note_template'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='virus',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='virus',
            name='description',
        ),
        migrations.RemoveField(
            model_name='virus',
            name='encryption_type',
        ),
        migrations.RemoveField(
            model_name='virus',
            name='file',
        ),
        migrations.RemoveField(
            model_name='virus',
            name='is_active',
        ),
        migrations.RemoveField(
            model_name='virus',
            name='ransom_note_template',
        ),
        migrations.RemoveField(
            model_name='virus',
            name='virus_type',
        ),
        migrations.AddField(
            model_name='virus',
            name='attack_behavior',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='攻击行为'),
        ),
        migrations.AddField(
            model_name='virus',
            name='count_affected_files_only',
            field=models.BooleanField(default=False, verbose_name='只统计受影响文件数，不做加密行为'),
        ),
        migrations.AddField(
            model_name='virus',
            name='custom_version_note',
            field=models.TextField(blank=True, null=True, verbose_name='定制版说明'),
        ),
        migrations.AddField(
            model_name='virus',
            name='delete_files_after_encryption',
            field=models.BooleanField(default=False, verbose_name='加密后删除原文件'),
        ),
        migrations.AddField(
            model_name='virus',
            name='enable_virus_evolution',
            field=models.BooleanField(default=False, verbose_name='是否开启病毒进化'),
        ),
        migrations.AddField(
            model_name='virus',
            name='encryption_files_suffix',
            field=models.TextField(blank=True, null=True, verbose_name='加密文件后缀'),
        ),
        migrations.AddField(
            model_name='virus',
            name='encryption_path',
            field=models.TextField(blank=True, null=True, verbose_name='加密路径'),
        ),
        migrations.AddField(
            model_name='virus',
            name='encryptor',
            field=models.FileField(blank=True, null=True, upload_to='encryptor/', verbose_name='加密器'),
        ),
        migrations.AddField(
            model_name='virus',
            name='exercise_ip_pool',
            field=models.TextField(blank=True, null=True, verbose_name='参与演习的IP地址池'),
        ),
        migrations.AddField(
            model_name='virus',
            name='incubation_period',
            field=models.IntegerField(blank=True, null=True, verbose_name='潜伏期'),
        ),
        migrations.AddField(
            model_name='virus',
            name='infection',
            field=models.CharField(choices=[('RANSOMWARE', '勒索病毒'), ('TROJAN', '木马病毒'), ('WORM', '蠕虫病毒')], default='RANSOMWARE', max_length=20, verbose_name='感染类型'),
        ),
        migrations.AddField(
            model_name='virus',
            name='ip_discovery',
            field=models.CharField(blank=True, choices=[('MANUAL', '手工'), ('AUTOMATIC', '自动')], max_length=20, null=True, verbose_name='IP发现'),
        ),
        migrations.AddField(
            model_name='virus',
            name='is_automated_infection',
            field=models.BooleanField(default=False, verbose_name='开启自动化感染'),
        ),
        migrations.AddField(
            model_name='virus',
            name='is_negotiation_feature',
            field=models.BooleanField(default=False, verbose_name='是否开启谈判功能'),
        ),
        migrations.AddField(
            model_name='virus',
            name='outbreak_end_time',
            field=models.DateField(blank=True, null=True, verbose_name='集中爆发结束时间'),
        ),
        migrations.AddField(
            model_name='virus',
            name='outbreak_start_time',
            field=models.DateField(blank=True, null=True, verbose_name='集中爆发开始时间'),
        ),
        migrations.AddField(
            model_name='virus',
            name='ransom_note_name',
            field=models.CharField(blank=True, max_length=300, null=True, verbose_name='勒索信名称'),
        ),
        migrations.AddField(
            model_name='virus',
            name='source_code',
            field=models.TextField(blank=True, null=True, verbose_name='勒索信内容'),
        ),
        migrations.AddField(
            model_name='virus',
            name='suffix',
            field=models.CharField(blank=True, max_length=300, null=True, verbose_name='病毒后缀'),
        ),
        migrations.AddField(
            model_name='virus',
            name='virus_family',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='family.virusfamily', verbose_name='参考病毒家族'),
        ),
        migrations.AddField(
            model_name='virus',
            name='wallpaper',
            field=models.FileField(blank=True, null=True, upload_to='wallpaper/', verbose_name='壁纸'),
        ),
        migrations.AlterField(
            model_name='virus',
            name='name',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='名称'),
        ),
        migrations.AlterModelTable(
            name='virus',
            table='ls_virus',
        ),
        migrations.CreateModel(
            name='ResourceControlModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=200, verbose_name='方案名称')),
                ('max_cpu_usage', models.IntegerField(blank=True, null=True, verbose_name='最大CPU占比')),
                ('max_memory_usage', models.IntegerField(blank=True, null=True, verbose_name='最大内存占比')),
                ('virus', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resource_control', to='virus.virus', verbose_name='病毒')),
            ],
            options={
                'verbose_name': '资源控制',
                'verbose_name_plural': '资源控制',
                'db_table': 'ls_resource_control',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VirusEvolutionModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('untreated_time', models.IntegerField(verbose_name='S秒未处理')),
                ('resource_control', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='virus.resourcecontrolmodel', verbose_name='资源控制')),
                ('virus', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='virus.virus', verbose_name='病毒')),
            ],
            options={
                'verbose_name': '病毒进化',
                'verbose_name_plural': '病毒进化',
                'db_table': 'ls_virus_evolution',
                'ordering': ['-created_at'],
            },
        ),
    ]
