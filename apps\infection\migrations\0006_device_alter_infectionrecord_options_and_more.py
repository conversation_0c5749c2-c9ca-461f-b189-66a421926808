# Generated by Django 5.1.3 on 2024-12-21 16:10

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('infection', '0005_remove_phishingrecord_exercise_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Device',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('device_id', models.CharField(max_length=36, unique=True, verbose_name='设备ID')),
                ('first_seen', models.DateTimeField(default=django.utils.timezone.now, verbose_name='首次发现时间')),
                ('last_seen', models.DateTimeField(auto_now=True, verbose_name='最后活动时间')),
                ('infection_count', models.IntegerField(default=0, verbose_name='感染次数')),
            ],
            options={
                'verbose_name': '设备信息',
                'verbose_name_plural': '设备信息',
                'ordering': ['-last_seen'],
            },
        ),
        migrations.AlterModelOptions(
            name='infectionrecord',
            options={'ordering': ['-system_time'], 'verbose_name': '感染记录', 'verbose_name_plural': '感染记录'},
        ),
        migrations.RemoveField(
            model_name='infectionrecord',
            name='client_id',
        ),
        migrations.AlterField(
            model_name='infectionrecord',
            name='system_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='系统时间'),
        ),
        migrations.AddField(
            model_name='infectionrecord',
            name='device',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='infection_records', to='infection.device', verbose_name='关联设备'),
        ),
    ]
