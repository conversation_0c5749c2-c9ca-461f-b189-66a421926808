class DataFormatter:
    """数据格式化工具类"""
    
    @staticmethod
    def format_bytes(size):
        """格式化字节大小"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    
    @staticmethod
    def format_percentage(value, total):
        """格式化百分比"""
        if total == 0:
            return "0%"
        return f"{(value / total * 100):.1f}%"
    
    @staticmethod
    def format_duration(seconds):
        """格式化持续时间"""
        if seconds < 60:
            return f"{seconds}秒"
        elif seconds < 3600:
            minutes = seconds // 60
            return f"{minutes}分钟"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours}小时{minutes}分钟" 