import PyInstaller.__main__
import os
import shutil

def build_agent():
    """构建资产监控代理程序"""
    # 清理旧的构建文件
    if os.path.exists('build'):
        shutil.rmtree('build')
    if os.path.exists('dist'):
        shutil.rmtree('dist')

    # PyInstaller参数
    args = [
        'asset_monitor.py',  # 主程序文件
        '--onefile',         # 打包成单个可执行文件
        '--name=asset_monitor',  # 输出文件名
        '--clean',           # 清理临时文件
        '--log-level=INFO',  # 日志级别
        # 添加图标（如果有）
        # '--icon=icon.ico',
    ]

    # 运行PyInstaller
    PyInstaller.__main__.run(args)

    print("Build completed! Check the 'dist' directory for the executable.")

if __name__ == '__main__':
    build_agent()
