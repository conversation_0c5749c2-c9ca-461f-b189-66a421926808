from django.contrib import admin
from .models import Exercise

@admin.register(Exercise)
class ExerciseAdmin(admin.ModelAdmin):
    list_display = ('name', 'virus', 'status', 'created_by')
    list_filter = ('status', 'created_at')
    search_fields = ('name', 'description')
    # date_hierarchy = 'start_time'
    # filter_horizontal = ('target_assets', 'target_groups')
    readonly_fields = ('created_at', 'updated_at')

    def get_readonly_fields(self, request, obj=None):
        """根据演练状态设置只读字段"""
        if obj and obj.status != Exercise.Status.PENDING:
            return self.readonly_fields + ('virus', 'target_assets', 'target_groups')
        return self.readonly_fields
