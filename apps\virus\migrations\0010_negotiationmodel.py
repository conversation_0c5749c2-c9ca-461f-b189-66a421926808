# Generated by Django 5.1.3 on 2024-12-23 17:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('virus', '0009_virus_encryptor_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='NegotiationModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('n_id', models.CharField(max_length=100, verbose_name='谈判配置手动填入的ID')),
                ('platform_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='平台名称')),
                ('company_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='公司名称')),
                ('official_website', models.CharField(blank=True, max_length=300, null=True, verbose_name='官网链接')),
                ('index_show_count', models.IntegerField(blank=True, null=True, verbose_name='首页展示数')),
                ('company_introduction', models.TextField(blank=True, null=True, verbose_name='公司介绍')),
                ('company_logo', models.FileField(blank=True, null=True, upload_to='company_logo/', verbose_name='病毒家族logo')),
                ('company_valuation', models.CharField(blank=True, max_length=20, null=True, verbose_name='公司估值')),
                ('stolen_data_volume', models.CharField(blank=True, max_length=100, null=True, verbose_name='窃取数据量')),
                ('ransom_amount', models.CharField(blank=True, max_length=100, null=True, verbose_name='赎金')),
                ('btc_address', models.CharField(blank=True, max_length=100, null=True, verbose_name='BTC地址')),
                ('usdt_address', models.CharField(blank=True, max_length=100, null=True, verbose_name='USDT地址')),
            ],
            options={
                'verbose_name': '谈判配置',
                'verbose_name_plural': '谈判配置',
                'db_table': 'ls_negotiation',
                'ordering': ['-created_at'],
            },
        ),
    ]
