from django_filters import rest_framework as filters
from .models import Exercise


class ExerciseFilter(filters.FilterSet):
    """演练任务筛选器"""

    name = filters.CharFilter(
        field_name="name",
        lookup_expr="icontains",
        help_text="按演练名称模糊搜索"
    )
    status = filters.ChoiceFilter(
        field_name="status",
        choices=Exercise.Status.choices,
        help_text="按演练状态筛选"
    )

    class Meta:
        model = Exercise
        fields = ['name', 'status']
