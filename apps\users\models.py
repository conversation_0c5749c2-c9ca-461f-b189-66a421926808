from config.models import BaseModel
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class User(AbstractUser, BaseModel):
    """自定义用户模型"""
    phone = models.CharField("手机号", max_length=11, blank=True)
    avatar = models.ImageField("头像", upload_to="avatar/", null=True, blank=True)
    last_login_ip = models.GenericIPAddressField(_("最后登录IP"), null=True, blank=True)

    class Meta:
        verbose_name = _("用户")
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.username

    def has_permission(self, permission):
        """检查用户是否有指定权限"""
        if self.is_superuser:
            return True
        if self.is_staff:
            return True
        return False


class SMSLog(BaseModel):
    """短信发送记录"""
    phone = models.Char<PERSON>ield(max_length=11, verbose_name="手机号")
    code = models.CharField(max_length=6, verbose_name="验证码")
    template_code = models.CharField(max_length=20, verbose_name="模板编号")
    status = models.CharField(max_length=50, verbose_name="发送状态")
    message = models.TextField(verbose_name="状态信息", null=True, blank=True)
    request_id = models.CharField(
        max_length=50, verbose_name="请求ID", null=True, blank=True
    )
    biz_id = models.CharField(
        max_length=50, verbose_name="业务ID", null=True, blank=True
    )

    class Meta:
        verbose_name = "短信记录"
        verbose_name_plural = verbose_name
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.phone} - {self.status}"
