from django.db import models
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from config.models import BaseModel

User = get_user_model()


class SystemConfig(BaseModel):
    """系统配置模型"""
    CONFIG_TYPE_CHOICES = (
        ('SE', '安全配置'),
        ('NO', '通知配置'),
        ('BA', '备份配置'),
        ('OT', '其他配置')
    )

    name = models.CharField('配置名称', max_length=100, unique=True)
    value = models.JSONField('配置值', default=dict)
    config_type = models.CharField('配置类型', max_length=2, choices=CONFIG_TYPE_CHOICES, default='OT')
    description = models.TextField('配置说明', blank=True)
    is_active = models.BooleanField('是否启用', default=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name='创建者'
    )

    class Meta:
        verbose_name = '系统配置'
        verbose_name_plural = verbose_name
        ordering = ['name']

    def __str__(self):
        return self.name

    def clean(self):
        """验证配置值"""
        if not isinstance(self.value, (dict, list)):
            raise ValidationError({
                'value': _('配置值必须是有效的JSON格式')
            })


class NotifyModel(BaseModel):
    """
    消息通知
    """
    exercise = models.ForeignKey(to="exercise.Exercise", verbose_name="项目模型", on_delete=models.CASCADE)
    user = models.ForeignKey(User, verbose_name=_("用户"), on_delete=models.CASCADE, null=True, blank=True)
    content = models.TextField(_("消息内容"), blank=True, null=True)
    is_read = models.BooleanField(verbose_name="是否已读", default=False)

    class Meta:
        db_table = "ls_notify"
        verbose_name = '系统消息'
        verbose_name_plural = verbose_name
        ordering = ['exercise']

    def __str__(self):
        return self.content
