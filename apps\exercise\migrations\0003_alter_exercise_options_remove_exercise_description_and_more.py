# Generated by Django 5.1.3 on 2025-01-02 10:42

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('exercise', '0002_initial'),
        ('phishing', '0013_emailtakmodel'),
        ('virus', '0013_alter_negotiationmodel_n_id'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='exercise',
            options={'ordering': ['-created_at'], 'verbose_name': '演练任务', 'verbose_name_plural': '演练任务'},
        ),
        migrations.RemoveField(
            model_name='exercise',
            name='description',
        ),
        migrations.RemoveField(
            model_name='exercise',
            name='end_time',
        ),
        migrations.RemoveField(
            model_name='exercise',
            name='start_time',
        ),
        migrations.RemoveField(
            model_name='exercise',
            name='target_assets',
        ),
        migrations.AddField(
            model_name='exercise',
            name='email_task',
            field=models.ForeignKey(blank=True, help_text='选择本次演练使用的钓鱼邮件任务', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='exercises', to='phishing.emailtakmodel', verbose_name='钓鱼任务'),
        ),
        migrations.AddField(
            model_name='exercise',
            name='exercise_time',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='演练时间'),
        ),
        migrations.AddField(
            model_name='exercise',
            name='negotiation',
            field=models.ForeignKey(blank=True, help_text='选择本次演练使用的谈判配置任务', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='exercises', to='virus.negotiationmodel', verbose_name='谈判配置'),
        ),
        migrations.AlterField(
            model_name='exercise',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_exercises', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AlterModelTable(
            name='exercise',
            table='ls_exercise',
        ),
    ]
