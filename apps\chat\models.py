from django.db import models
from django.conf import settings
from config.models import BaseModel


class Chat(BaseModel):
    """聊天会话模型"""
    negotiation = models.OneToOneField(verbose_name="谈判配置模型", to="virus.NegotiationModel",
                                       on_delete=models.CASCADE, related_name="chat", null=True, blank=True)
    title = models.CharField('标题', max_length=100, blank=True)
    conversation_id = models.CharField('会话ID', max_length=100, blank=True, null=True, help_text='Cozi的会话ID')
    # private_key = models.CharField('私钥', max_length=100, blank=True, null=True, help_text='用于后期用户验证')

    class Meta:
        db_table = 'ls_chat'
        verbose_name = '聊天会话'
        verbose_name_plural = verbose_name
        ordering = ['-updated_at']

    def __str__(self):
        return f"会话 - {self.title or self.id}"


class Message(BaseModel):
    """聊天消息模型"""
    ROLE_CHOICES = (
        ('user', '用户'),
        ('assistant', '助手'),
        ('artificial', '人工'),
        ('system', '系统'),
    )

    chat = models.ForeignKey(
        Chat,
        on_delete=models.CASCADE,
        related_name='messages',
        verbose_name='会话'
    )
    role = models.CharField('角色', max_length=10, choices=ROLE_CHOICES)
    content = models.TextField('内容')

    class Meta:
        db_table = "ls_message"
        verbose_name = '聊天消息'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.chat.title or self.chat.id} - {self.role}: {self.content[:20]}"
