from rest_framework.views import exception_handler
from rest_framework.exceptions import APIException
from rest_framework.response import Response
from rest_framework import status
from django.db.models import ProtectedError
from django.utils.translation import gettext as _

def custom_exception_handler(exc, context):
    # 处理 ProtectedError 异常
    if isinstance(exc, ProtectedError):
        # 解析受保护的对象信息
        protected_objects = exc.args[1] if len(exc.args) > 1 else set()

        # 提取演练项目信息
        exercise_names = []
        for obj in protected_objects:
            if hasattr(obj, '__class__') and obj.__class__.__name__ == 'Exercise':
                # 提取演练项目名称，去掉状态信息
                obj_str = str(obj)
                # 移除 " (已完成)" 等状态信息
                clean_name = obj_str.split(' (')[0] if ' (' in obj_str else obj_str
                exercise_names.append(clean_name)

        # 构建错误响应
        if exercise_names:
            error_message = f"无法删除该病毒，因为它正在被以下演练项目使用：{', '.join(exercise_names)}。请先删除这些演练项目后再试。"
        else:
            error_message = "无法删除该对象，因为它正在被其他项目使用。请先删除相关项目后再试。"

        return Response(
            {
                'detail': error_message,
                'error_type': 'protected_error',
                'protected_objects': exercise_names
            },
            status=status.HTTP_400_BAD_REQUEST
        )

    response = exception_handler(exc, context)

    if response is not None:
        # 自定义错误消息映射
        error_messages = {
            'No active account found with the given credentials': '用户名或密码错误',
            'Invalid token': '无效的令牌',
            'Token has expired': '令牌已过期',
            'Authentication credentials were not provided': '未提供认证凭据',
            'User account is disabled': '用户账号已被禁用',
            'Given token not valid for any token type': '令牌已过期,请重新登录',
            'Invalid verification code': '验证码错误',
            'User not found': '用户不存在',
            'Invalid phone number': '无效的手机号码',
            'Invalid verification code': '验证码错误',
            'User not found': '用户不存在',
        }
        
        # 获取原始错误消息
        if isinstance(response.data, dict):
            original_detail = str(response.data.get('detail', ''))
        else:
            original_detail = str(response.data)
            
        # 替换为中文错误消息
        if original_detail in error_messages:
            if isinstance(response.data, dict):
                response.data['detail'] = error_messages[original_detail]
            else:
                response.data = {'detail': error_messages[original_detail]}
    
    return response
