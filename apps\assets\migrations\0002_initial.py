# Generated by Django 5.1.3 on 2024-11-21 08:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('assets', '0001_initial'),
        ('users', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='asset',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_assets', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddField(
            model_name='asset',
            name='department',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.department', verbose_name='所属部门'),
        ),
        migrations.AddField(
            model_name='assetgroup',
            name='assets',
            field=models.ManyToManyField(related_name='groups', to='assets.asset', verbose_name='资产列表'),
        ),
        migrations.AddField(
            model_name='assetgroup',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_asset_groups', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddField(
            model_name='assetrelation',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_relations', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddField(
            model_name='assetrelation',
            name='source',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_relations', to='assets.asset', verbose_name='源资产'),
        ),
        migrations.AddField(
            model_name='assetrelation',
            name='target',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='target_relations', to='assets.asset', verbose_name='目标资产'),
        ),
        migrations.AddField(
            model_name='assetstatus',
            name='asset',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assets.asset', verbose_name='资产'),
        ),
        migrations.AddField(
            model_name='asset',
            name='tags',
            field=models.ManyToManyField(blank=True, to='assets.assettag', verbose_name='标签'),
        ),
        migrations.AddField(
            model_name='assettopology',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_topologies', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddField(
            model_name='topologyasset',
            name='asset',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assets.asset', verbose_name='资产'),
        ),
        migrations.AddField(
            model_name='topologyasset',
            name='topology',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assets.assettopology', verbose_name='所属拓扑'),
        ),
        migrations.AddField(
            model_name='assettopology',
            name='assets',
            field=models.ManyToManyField(through='assets.TopologyAsset', to='assets.asset', verbose_name='包含资产'),
        ),
        migrations.AlterUniqueTogether(
            name='assetrelation',
            unique_together={('source', 'target', 'relation_type')},
        ),
        migrations.AlterUniqueTogether(
            name='topologyasset',
            unique_together={('topology', 'asset')},
        ),
    ]
