from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.dashboard.tasks import update_dashboard_metrics
from apps.exercise.models import Exercise

class Command(BaseCommand):
    help = '更新所有演练的仪表盘指标'

    def add_arguments(self, parser):
        parser.add_argument(
            '--exercise',
            type=int,
            help='指定演练ID，不指定则更新所有演练'
        )

    def handle(self, *args, **options):
        exercise_id = options.get('exercise')
        start_time = timezone.now()
        
        try:
            if exercise_id:
                exercise = Exercise.objects.get(id=exercise_id)
                self.stdout.write(f'开始更新演练 "{exercise.name}" 的指标...')
                update_dashboard_metrics.delay(exercise_id)
            else:
                self.stdout.write('开始更新所有演练的指标...')
                update_dashboard_metrics.delay()
            
            duration = timezone.now() - start_time
            self.stdout.write(
                self.style.SUCCESS(
                    f'指标更新任务已提交，耗时: {duration.total_seconds():.2f}秒'
                )
            )
            
        except Exercise.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'演练ID {exercise_id} 不存在')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'更新指标时出错: {str(e)}')
            ) 