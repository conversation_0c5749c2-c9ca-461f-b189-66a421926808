from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import User, SMSLog

@admin.register(User)
class CustomUserAdmin(UserAdmin):
    list_display = ('username', 'email', 'last_login_ip', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('username', 'email')
    
    fieldsets = UserAdmin.fieldsets + (
        ('额外信息', {'fields': ('last_login_ip',)}),
    )

@admin.register(SMSLog)
class SMSLogAdmin(admin.ModelAdmin):
    list_display = ['phone', 'status', 'created_at', 'message']
    list_filter = ['status', 'created_at']
    search_fields = ['phone', 'message']
    readonly_fields = ['phone', 'code', 'template_code', 'status', 'message', 'request_id', 'biz_id', 'created_at']
    ordering = ['-created_at']
