# Generated by Django 5.1.3 on 2024-12-11 08:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('phishing', '0003_emailtemplatemodel'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailTemplateFileModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('email_file', models.FileField(blank=True, null=True, upload_to='email_template/', verbose_name='头像')),
                ('zip_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='压缩包名称')),
                ('zip_password', models.CharField(blank=True, max_length=100, null=True, verbose_name='压缩包密码')),
            ],
            options={
                'verbose_name': '邮件模板附件',
                'verbose_name_plural': '邮件模板附件',
                'db_table': 'ls_email_template_file',
                'ordering': ['-id'],
            },
        ),
        migrations.AddField(
            model_name='emailtemplatemodel',
            name='is_redirect_url',
            field=models.BooleanField(default=False, verbose_name='自动跳转URL'),
        ),
        migrations.AddField(
            model_name='emailtemplatemodel',
            name='is_track_user',
            field=models.BooleanField(default=False, verbose_name='是否跟踪用户'),
        ),
        migrations.AddField(
            model_name='emailtemplatemodel',
            name='email_template_file',
            field=models.ManyToManyField(blank=True, db_table='ls_email_template_to_files', to='phishing.emailtemplatefilemodel', verbose_name='邮件模板附件'),
        ),
    ]
