# Generated by Django 5.1.3 on 2024-11-21 08:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('assets', '0001_initial'),
        ('dashboard', '0001_initial'),
        ('exercise', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='alerthistory',
            name='exercise',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='exercise.exercise', verbose_name='相关演练'),
        ),
        migrations.AddField(
            model_name='assetstatus',
            name='asset',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_history', to='assets.asset'),
        ),
    ]
