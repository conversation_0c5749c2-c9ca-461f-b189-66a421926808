from django.utils import timezone
from django.db.models import Count, Q
from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, extend_schema_view
from datetime import timedelta

from .models import DashboardStatistics, ExerciseStatistics, AssetStatistics
from .serializers import (
    DashboardStatisticsSerializer, 
    ExerciseStatisticsSerializer,
    AssetStatisticsSerializer,
    DashboardOverviewSerializer
)
from apps.exercise.models import Exercise
from apps.assets.models import Asset, AssetGroup
from apps.infection.models import InfectionRecord

@extend_schema_view(
    overview=extend_schema(
        summary="获取数据看板概览",
        description="获取数据看板的整体概览数据",
        tags=["数据看板"]
    ),
    exercise_trend=extend_schema(
        summary="获取演练趋势",
        description="获取演练相关的趋势数据",
        tags=["数据看板"]
    ),
    asset_statistics=extend_schema(
        summary="获取资产统计",
        description="获取资产相关的统计数据",
        tags=["数据看板"]
    )
)
class DashboardViewSet(viewsets.ModelViewSet):
    """数据看板视图集"""
    permission_classes = [permissions.IsAuthenticated]
    queryset = DashboardStatistics.objects.all()
    serializer_class = DashboardStatisticsSerializer
    
    def _get_today_stats(self):
        """获取今日统计数据"""
        today = timezone.now().date()
        stats, _ = DashboardStatistics.objects.get_or_create(date=today)
        
        # 更新统计数据
        stats.total_exercises = Exercise.objects.exclude(status='TE').count()  # 总演练数(排除已终止)
        stats.active_exercises = Exercise.objects.filter(status='RU').count()  # 进行中的演练数
        stats.finished_exercises = Exercise.objects.filter(status='FI').count()  # 已完成的演练数
        stats.total_assets = Asset.objects.count()  # 总资产数
        
        # 获取最新的感染记录
        infected_assets = Asset.objects.filter(
            id__in=InfectionRecord.objects.filter(
                status='infected'
            ).values_list('device_id', flat=True)
        ).count()
        stats.infected_assets = infected_assets  # 已感染资产数
        
        stats.total_asset_groups = AssetGroup.objects.count()  # 资产组数
        stats.save()
        
        return stats
    
    def _get_exercise_trend(self, days=7):
        """获取演练趋势数据"""
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days-1)
        return ExerciseStatistics.objects.filter(
            date__range=[start_date, end_date]
        ).order_by('date')
    
    def _get_asset_distribution(self):
        """获取资产分布数据"""
        today = timezone.now().date()
        return AssetStatistics.objects.filter(date=today)
    
    def _get_status_distribution(self):
        """获取状态分布数据"""
        status_counts = Exercise.objects.values('status').annotate(
            count=Count('id')
        )
        return {
            item['status']: item['count']
            for item in status_counts
        }
    
    @action(detail=False, methods=['get'])
    def overview(self, request):
        """获取数据看板概览"""
        data = {
            'today_stats': self._get_today_stats(),
            'exercise_trend': self._get_exercise_trend(),
            'asset_distribution': self._get_asset_distribution(),
            'status_distribution': self._get_status_distribution()
        }
        serializer = DashboardOverviewSerializer(data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def exercise_trend(self, request):
        """获取演练趋势"""
        days = int(request.query_params.get('days', 7))
        trend_data = self._get_exercise_trend(days)
        serializer = ExerciseStatisticsSerializer(trend_data, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def asset_statistics(self, request):
        """获取资产统计"""
        asset_data = self._get_asset_distribution()
        serializer = AssetStatisticsSerializer(asset_data, many=True)
        return Response(serializer.data)