# Generated by Django 5.1.3 on 2025-01-02 11:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assets', '0006_alter_asset_group_alter_asset_table'),
        ('exercise', '0003_alter_exercise_options_remove_exercise_description_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='exercise',
            name='target_asset',
            field=models.ManyToManyField(blank=True, help_text='选择本次邮箱信息组', related_name='assets_exercises', to='assets.assetgroup', verbose_name='邮箱信息组'),
        ),
        migrations.AlterField(
            model_name='exercise',
            name='target_groups',
            field=models.ManyToManyField(blank=True, help_text='选择本次演练的目标资产组', related_name='groups_exercises', to='assets.assetgroup', verbose_name='目标资产组'),
        ),
    ]
