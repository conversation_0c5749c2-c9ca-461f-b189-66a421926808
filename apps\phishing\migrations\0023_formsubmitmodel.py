# Generated by Django 5.1.3 on 2025-03-18 10:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('phishing', '0022_emaillog_user_flag'),
    ]

    operations = [
        migrations.CreateModel(
            name='FormSubmitModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('for_data', models.J<PERSON>NField(default=dict, verbose_name='表单提交信息')),
                ('email_log', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='phishing.emaillog', verbose_name='邮件记录')),
            ],
            options={
                'verbose_name': '表单提交数据',
                'verbose_name_plural': '表单提交数据',
                'db_table': 'ls_form_submit',
                'ordering': ['-created_at'],
            },
        ),
    ]
