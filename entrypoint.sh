#!/bin/bash

# 设置错误时退出
set -e

echo "开始启动Django应用..."

# 确保DJANGO_ENV环境变量被正确设置
if [ -z "$DJANGO_ENV" ]; then
    # 在容器环境中，默认使用product环境
    if [ -f /.dockerenv ]; then
        export DJANGO_ENV=product
    else
        export DJANGO_ENV=develop
    fi
fi

echo "当前环境: $DJANGO_ENV"

# 设置目录和权限
mkdir -p /app/logs /app/media /app/static /tmp
chown -R appuser:appuser /app
chmod +x /app/manage.py

# 数据库迁移操作
echo "开始数据库迁移..."
# 等待数据库连接
sudo -u appuser -H --preserve-env bash -c "cd /app && python manage.py check --database default" || {
    sleep 5
    sudo -u appuser -H --preserve-env bash -c "cd /app && python manage.py check --database default"
}

# 数据库迁移
sudo -u appuser -H --preserve-env bash -c "cd /app && python manage.py makemigrations"
sudo -u appuser -H --preserve-env bash -c "cd /app && python manage.py migrate"

# 收集静态文件
sudo -u appuser -H --preserve-env bash -c "cd /app && python manage.py collectstatic --noinput"

# 创建超级用户
ADMIN_USERNAME=${ADMIN_USERNAME:-admin}
ADMIN_EMAIL=${ADMIN_EMAIL:-<EMAIL>}
ADMIN_PASSWORD=${ADMIN_PASSWORD:-Admin@123456}

sudo -u appuser -H --preserve-env bash -c "cd /app && python manage.py shell -c \"
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(is_superuser=True).exists():
    User.objects.create_superuser('$ADMIN_USERNAME', '$ADMIN_EMAIL', '$ADMIN_PASSWORD')
    print('已创建默认超级用户')
else:
    print('超级用户已存在')
\""

# 启动应用服务
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
