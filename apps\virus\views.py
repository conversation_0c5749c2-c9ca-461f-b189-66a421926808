from django.db.models import Q, ProtectedError
from django.http import HttpResponse, HttpResponseRedirect, Http404
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from user_agents import parse
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter

from extensions import queryset_utils, constants
from .models import Virus, RansomTemplate, NegotiationModel
from .serializers import (
    VirusSerializer, RansomTemplateSerializer,
    RansomTemplatePreviewSerializer, NegotiationSerializer
)
from . import filters as c_filter
from django.core.files.storage import default_storage
import os
import uuid
from ..chat.models import Chat
from ..chat.utils import AIClient
from ..phishing.models import EmailLog, FormSubmitModel


@extend_schema_view(
    list=extend_schema(
        summary="获取病毒样本列表",
        description="获取所有可用的病毒样本列表",
        tags=["病毒管理"],
        responses={200: VirusSerializer(many=True)}
    ),
    create=extend_schema(
        summary="创建病毒样本",
        description="创建新的病毒样本，支持上传加密器和壁纸文件",
        tags=["病毒管理"],
        request=VirusSerializer,
        responses={
            201: VirusSerializer,
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '创建失败的错误信息'}
                }
            }
        }
    ),
    retrieve=extend_schema(
        summary="获取病毒样本详情",
        description="获取指定病毒样本的详细信息",
        tags=["病毒管理"],
        responses={
            200: VirusSerializer,
            404: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '样本不存在'}
                }
            }
        }
    ),
    update=extend_schema(
        summary="更新病毒样本",
        description="更新指定病毒样本的全部信息",
        tags=["病毒管理"],
        request=VirusSerializer,
        responses={
            200: VirusSerializer,
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '更新失败的错误信息'}
                }
            },
            404: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '样本不存在'}
                }
            }
        }
    ),
    partial_update=extend_schema(
        summary="部分更新病毒样本",
        description="部分更新指定病毒样本的信息",
        tags=["病毒管理"],
        request=VirusSerializer,
        responses={
            200: VirusSerializer,
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '更新失败的错误信息'}
                }
            },
            404: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '样本不存在'}
                }
            }
        }
    ),
    destroy=extend_schema(
        summary="删除病毒样本",
        description="删除指定的病毒样本",
        tags=["病毒管理"],
        responses={
            204: None,
            404: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '样本不存在'}
                }
            }
        }
    )
)
class VirusViewSet(queryset_utils.UserOwnedModelViewSet):
    """病毒样本管理视图集"""
    queryset = Virus.objects.all()
    serializer_class = VirusSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = c_filter.VirusFilter

    def create(self, request, *args, **kwargs):
        """创建病毒样本"""
        # 创建request.data的可变副本
        mutable_data = request.data.copy()

        # 处理文件上传
        if 'encryptor' in request.FILES:
            mutable_data['encryptor'] = request.FILES['encryptor']
        if 'wallpaper' in request.FILES:
            mutable_data['wallpaper'] = request.FILES['wallpaper']

        # 使用可变数据创建新的请求对象
        request._full_data = mutable_data
        return super().create(request, *args, **kwargs)



    def update(self, request, *args, **kwargs):
        """更新病毒样本"""
        # 处理文件上传
        if 'encryptor' in request.FILES:
            request.data['encryptor'] = request.FILES['encryptor']
        if 'wallpaper' in request.FILES:
            request.data['wallpaper'] = request.FILES['wallpaper']
            
        return super().update(request, *args, **kwargs)

@extend_schema_view(
    list=extend_schema(
        summary="获取勒索信模板列表",
        description="获取所有可用的勒索信模板列表",
        tags=["勒索信模板"],
        responses={200: RansomTemplateSerializer(many=True)}
    ),
    create=extend_schema(
        summary="创建勒索信模板",
        description="创建新的勒索信模板",
        tags=["勒索信模板"],
        request=RansomTemplateSerializer,
        responses={
            201: RansomTemplateSerializer,
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '创建失败的错误信息'}
                }
            }
        }
    ),
    retrieve=extend_schema(
        summary="获取勒索信模板详情",
        description="获取指定勒索信模板的详细信息",
        tags=["勒索信模板"],
        responses={
            200: RansomTemplateSerializer,
            404: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '模板不存在'}
                }
            }
        }
    ),
    update=extend_schema(
        summary="更新勒索信模板",
        description="更新指定勒索信模板的全部信息",
        tags=["勒索信模板"],
        request=RansomTemplateSerializer,
        responses={
            200: RansomTemplateSerializer,
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '更新失败的错误信息'}
                }
            },
            404: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '模板不存在'}
                }
            }
        }
    ),
    partial_update=extend_schema(
        summary="部分更新勒索信模板",
        description="部分更新指定勒索信模板的信息",
        tags=["勒索信模板"],
        request=RansomTemplateSerializer,
        responses={
            200: RansomTemplateSerializer,
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '更新失败的错误信息'}
                }
            },
            404: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '模板不存在'}
                }
            }
        }
    ),
    destroy=extend_schema(
        summary="删除勒索信模板",
        description="删除指定的勒索信模板",
        tags=["勒索信模板"],
        responses={
            204: None,
            404: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '模板不存在'}
                }
            }
        }
    )
)
class RansomTemplateViewSet(viewsets.ModelViewSet):
    """勒索信模板管理视图集"""
    queryset = RansomTemplate.objects.all()
    serializer_class = RansomTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    @extend_schema(
        summary="预览勒索信",
        description="预览指定勒索信模板的渲染效果",
        tags=["勒索信模板"],
        parameters=[
            OpenApiParameter(
                name='lang',
                description='语言(zh/en)',
                type=str,
                required=False,
                default='zh',
                enum=['zh', 'en']
            ),
            OpenApiParameter(
                name='variables',
                description='模板变量(JSON格式)',
                type=dict,
                required=False
            )
        ],
        responses={
            200: RansomTemplatePreviewSerializer,
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '渲染错误信息'}
                }
            },
            404: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '模板不存在'}
                }
            }
        }
    )
    @action(detail=True, methods=['get'])
    def preview(self, request, pk=None):
        """预览勒索信模板"""
        template = self.get_object()
        lang = request.query_params.get('lang', 'zh')
        variables = request.query_params.get('variables', {})
        
        try:
            rendered_content = template.render(lang, variables)
            return Response({
                'content': rendered_content,
                'language': lang,
                'variables': variables
            })
        except Exception as e:
            return Response(
                {'error': f'渲染模板失败: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @extend_schema(
        summary="导出模板",
        description="导出勒索信模板到文件",
        tags=["勒索信模板"],
        parameters=[
            OpenApiParameter(
                name='format',
                description='导出格式(json/html)',
                type=str,
                default='json',
                enum=['json', 'html']
            ),
            OpenApiParameter(
                name='lang',
                description='语言(zh/en)',
                type=str,
                required=False,
                default='zh',
                enum=['zh', 'en']
            )
        ],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'file_url': {'type': 'string', 'description': '文件下载地址'},
                    'file_name': {'type': 'string', 'description': '文件名称'},
                    'file_size': {'type': 'integer', 'description': '文件大小(字节)'}
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '导出错误信息'}
                }
            }
        }
    )
    @action(detail=True, methods=['get'])
    def export(self, request, pk=None):
        """导出勒索信模板"""
        template = self.get_object()
        format_type = request.query_params.get('format', 'json')
        lang = request.query_params.get('lang', 'zh')
        
        try:
            return template.export(format_type, lang)
        except Exception as e:
            return Response(
                {'error': f'导出失败: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @extend_schema(
        summary="克隆模板",
        description="克隆现有的勒索信模板",
        tags=["勒索信模板"],
        request={
            'type': 'object',
            'properties': {
                'name': {'type': 'string', 'description': '新模板名称'},
                'description': {'type': 'string', 'description': '新模板描述'}
            },
            'required': ['name']
        },
        responses={
            201: RansomTemplateSerializer,
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '克隆错误信息'}
                }
            }
        }
    )
    @action(detail=True, methods=['post'])
    def clone(self, request, pk=None):
        """克隆勒索信模板"""
        template = self.get_object()
        name = request.data.get('name')
        description = request.data.get('description', '')
        
        if not name:
            return Response(
                {'error': '请提供新模板名称'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            new_template = template.clone(
                name=name,
                description=description,
                created_by=request.user
            )
            return Response(
                RansomTemplateSerializer(new_template).data,
                status=status.HTTP_201_CREATED
            )
        except Exception as e:
            return Response(
                {'error': f'克隆失败: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @extend_schema(
        summary="验证模板",
        description="验证勒索信模板的语法和变量",
        tags=["勒索信模板"],
        request={
            'type': 'object',
            'properties': {
                'variables': {
                    'type': 'object',
                    'description': '测试变量'
                }
            }
        },
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'is_valid': {'type': 'boolean', 'description': '是否有效'},
                    'errors': {
                        'type': 'array',
                        'description': '错误列表',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'type': {'type': 'string', 'description': '错误类型'},
                                'message': {'type': 'string', 'description': '错误信息'},
                                'location': {'type': 'string', 'description': '错误位置'}
                            }
                        }
                    }
                }
            }
        }
    )
    @action(detail=True, methods=['post'])
    def validate(self, request, pk=None):
        """验证勒索信模板"""
        template = self.get_object()
        variables = request.data.get('variables', {})
        
        validation_result = template.validate(variables)
        return Response(validation_result)

    @extend_schema(
        summary="获取模板统计",
        description="获取勒索信模板的使用统计",
        tags=["勒索信模板"],
        parameters=[
            OpenApiParameter(
                name='start_time',
                description='开始时间(YYYY-MM-DD)',
                type=str,
                required=False
            ),
            OpenApiParameter(
                name='end_time',
                description='结束时间(YYYY-MM-DD)',
                type=str,
                required=False
            )
        ],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'usage_count': {'type': 'integer', 'description': '使用次数'},
                    'success_rate': {'type': 'number', 'description': '成功率(%)'},
                    'avg_ransom_amount': {'type': 'number', 'description': '平均勒索金额'},
                    'language_distribution': {
                        'type': 'object',
                        'description': '语言分布',
                        'properties': {
                            'zh': {'type': 'integer', 'description': '中文使用次数'},
                            'en': {'type': 'integer', 'description': '英文使用次数'}
                        }
                    },
                    'time_distribution': {
                        'type': 'array',
                        'description': '时间分布',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'date': {'type': 'string', 'description': '日期'},
                                'count': {'type': 'integer', 'description': '使用次数'}
                            }
                        }
                    }
                }
            }
        }
    )
    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """获取模板统计信息"""
        template = self.get_object()
        start_time = request.query_params.get('start_time')
        end_time = request.query_params.get('end_time')
        
        return Response(
            template.get_statistics(start_time, end_time)
        )

    @extend_schema(
        summary="获取模板变量",
        description="获勒索信模板支持的变量列表",
        tags=["勒索信模板"],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'variables': {
                        'type': 'array',
                        'description': '支持的变量列表',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'name': {'type': 'string', 'description': '变量名称'},
                                'description': {'type': 'string', 'description': '变量说明'},
                                'required': {'type': 'boolean', 'description': '是否必填'},
                                'default': {'type': 'string', 'description': '默认值'},
                                'example': {'type': 'string', 'description': '示例值'}
                            }
                        }
                    }
                }
            }
        }
    )
    @action(detail=True, methods=['get'])
    def variables(self, request, pk=None):
        """获取模板变量列表"""
        template = self.get_object()
        return Response({
            'variables': template.get_variables()
        })

    @extend_schema(
        summary="获取模板使用历史",
        description="获取勒索信模板的使用历史记录",
        tags=["勒索信模板"],
        parameters=[
            OpenApiParameter(
                name='start_time',
                description='开始时间(YYYY-MM-DD)',
                type=str,
                required=False
            ),
            OpenApiParameter(
                name='end_time',
                description='结束时间(YYYY-MM-DD)',
                type=str,
                required=False
            )
        ],
        responses={
            200: {
                'type': 'array',
                'items': {
                    'type': 'object',
                    'properties': {
                        'exercise': {'type': 'string', 'description': '演练名称'},
                        'used_time': {'type': 'string', 'description': '使用时间'},
                        'language': {'type': 'string', 'description': '使用语言'},
                        'success_rate': {'type': 'number', 'description': '成功率(%)'},
                        'target_count': {'type': 'integer', 'description': '目标数量'}
                    }
                }
            }
        }
    )
    @action(detail=True, methods=['get'])
    def usage_history(self, request, pk=None):
        """获取模板使用历史"""
        template = self.get_object()
        start_time = request.query_params.get('start_time')
        end_time = request.query_params.get('end_time')
        
        return Response(
            template.get_usage_history(start_time, end_time)
        )

    @extend_schema(
        summary="获取模板效果分析",
        description="获取勒索信模板的效果分析数据",
        tags=["勒索信模板"],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'overall_stats': {
                        'type': 'object',
                        'properties': {
                            'total_uses': {'type': 'integer', 'description': '总使用次数'},
                            'avg_success_rate': {'type': 'number', 'description': '平均成功率(%)'},
                            'avg_response_time': {'type': 'number', 'description': '平均响应时间(分钟)'}
                        }
                    },
                    'language_stats': {
                        'type': 'object',
                        'properties': {
                            'zh': {
                                'type': 'object',
                                'properties': {
                                    'uses': {'type': 'integer', 'description': '使用次数'},
                                    'success_rate': {'type': 'number', 'description': '成功率(%)'}
                                }
                            },
                            'en': {
                                'type': 'object',
                                'properties': {
                                    'uses': {'type': 'integer', 'description': '使用次数'},
                                    'success_rate': {'type': 'number', 'description': '成功率(%)'}
                                }
                            }
                        }
                    },
                    'target_analysis': {
                        'type': 'array',
                        'description': '目标群体分析',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'group': {'type': 'string', 'description': '目标群体'},
                                'success_rate': {'type': 'number', 'description': '成功率(%)'},
                                'avg_ransom': {'type': 'number', 'description': '平均勒索金额'}
                            }
                        }
                    },
                    'improvement_suggestions': {
                        'type': 'array',
                        'description': '改进建议',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'aspect': {'type': 'string', 'description': '改进方面'},
                                'suggestion': {'type': 'string', 'description': '建议内容'},
                                'expected_improvement': {'type': 'string', 'description': '预期改进效果'}
                            }
                        }
                    }
                }
            }
        }
    )
    @action(detail=True, methods=['get'])
    def effectiveness_analysis(self, request, pk=None):
        """获取模板效果分析"""
        template = self.get_object()
        return Response(
            template.analyze_effectiveness()
        )

    @extend_schema(
        summary="比较模板效果",
        description="比较多个勒索信模板的效果",
        tags=["勒索信模板"],
        request={
            'type': 'object',
            'properties': {
                'template_ids': {
                    'type': 'array',
                    'items': {'type': 'integer'},
                    'description': '要比较的模板ID列表'
                },
                'metrics': {
                    'type': 'array',
                    'items': {'type': 'string'},
                    'description': '比较指标(success_rate/response_time/ransom_amount)'
                }
            },
            'required': ['template_ids']
        },
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'comparison_results': {
                        'type': 'array',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'template_id': {'type': 'integer', 'description': '模板ID'},
                                'template_name': {'type': 'string', 'description': '模板名称'},
                                'metrics': {
                                    'type': 'object',
                                    'properties': {
                                        'success_rate': {'type': 'number', 'description': '成功率(%)'},
                                        'response_time': {'type': 'number', 'description': '响应时间(分钟)'},
                                        'ransom_amount': {'type': 'number', 'description': '勒索金额'}
                                    }
                                }
                            }
                        }
                    },
                    'best_performer': {
                        'type': 'object',
                        'properties': {
                            'template_id': {'type': 'integer', 'description': '模板ID'},
                            'template_name': {'type': 'string', 'description': '模板名称'},
                            'reason': {'type': 'string', 'description': '最佳原因'}
                        }
                    }
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '错误信息'}
                }
            }
        }
    )
    @action(detail=False, methods=['post'])
    def compare(self, request):
        """比较模板效果"""
        template_ids = request.data.get('template_ids', [])
        metrics = request.data.get('metrics', ['success_rate'])
        
        if not template_ids:
            return Response(
                {'error': '请提供要比较的模板ID列表'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            templates = RansomTemplate.objects.filter(id__in=template_ids)
            if templates.count() != len(template_ids):
                return Response(
                    {'error': '部分模板不存在'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            comparison_results = []
            for template in templates:
                result = {
                    'template_id': template.id,
                    'template_name': template.name,
                    'metrics': template.get_metrics(metrics)
                }
                comparison_results.append(result)
                
            # 确定最佳表现的模板
            best_performer = max(
                comparison_results,
                key=lambda x: x['metrics'].get('success_rate', 0)
            )
            
            return Response({
                'comparison_results': comparison_results,
                'best_performer': {
                    'template_id': best_performer['template_id'],
                    'template_name': best_performer['template_name'],
                    'reason': '最高成功率'
                }
            })
            
        except Exception as e:
            return Response(
                {'error': f'比较失败: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )


@extend_schema_view(
    create=extend_schema(
        summary="上传文件",
        description="上传病毒相关的文件（加密器、壁纸等）",
        tags=["病毒管理"],
        responses={
            201: {
                'type': 'object',
                'properties': {
                    'url': {'type': 'string', 'description': '文件访问URL'},
                    'path': {'type': 'string', 'description': '文件存储路径'}
                }
            }
        }
    )
)
class VirusFileUploadViewSet(viewsets.ViewSet):
    """病毒文件上传视图集"""
    permission_classes = [permissions.IsAuthenticated]

    def create(self, request, *args, **kwargs):
        """上传文件"""
        file = request.FILES.get('file')
        file_type = request.data.get('type')  # encryptor 或 wallpaper

        if not file:
            return Response(
                {'error': '请选择要上传的文件'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not file_type or file_type not in ['encryptor', 'wallpaper']:
            return Response(
                {'error': '无效的文件类型'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 生成唯一文件名
        file_ext = os.path.splitext(file.name)[1]
        filename = f"{file_type}/{str(uuid.uuid4()).replace('-', '')}{file_ext}"

        try:
            # 保存文件
            file_path = default_storage.save(filename, file)
            # 获取文件URL
            file_url = default_storage.url(file_path)
            file_url = request.build_absolute_uri(file_url)

            return Response({
                'url': file_url,
                'path': file_path
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {'error': f'文件上传失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@extend_schema_view(
    list=extend_schema(
        summary="获取谈判配置列表",
        description="获取所有谈判配置信息列表",
        tags=["谈判管理"],
        parameters=[
            OpenApiParameter(
                name="search",
                description="搜索关键词(平台名称/公司介绍)",
                type=str,
                required=False
            )
        ],
        responses={200: NegotiationSerializer(many=True)}
    ),
    create=extend_schema(
        summary="创建谈判配置",
        description="创建新的谈判配置,同时会创建对应的聊天会话",
        tags=["谈判管理"],
        request=NegotiationSerializer,
        responses={
            201: NegotiationSerializer,
            400: {"type": "object", "properties": {"error": {"type": "string"}}}
        }
    ),
    retrieve=extend_schema(
        summary="获取谈判配置详情",
        description="获取指定谈判配置的详细信息(支持通过id或n_id获取)",
        tags=["谈判管理"],
        responses={
            200: NegotiationSerializer,
            404: {"type": "object", "properties": {"error": {"type": "string"}}}
        }
    ),
    update=extend_schema(
        summary="更新谈判配置",
        description="更新指定谈判配置的信息",
        tags=["谈判管理"],
        request=NegotiationSerializer,
        responses={
            200: NegotiationSerializer,
            400: {"type": "object", "properties": {"error": {"type": "string"}}}
        }
    ),
    destroy=extend_schema(
        summary="删除谈判配置",
        description="删除指定谈判配置",
        tags=["谈判管理"],
        responses={
            204: None,
            404: {"type": "object", "properties": {"error": {"type": "string"}}}
        }
    ),
    partial_update=extend_schema(
        summary="部分更新谈判配置",
        description="部分更新指定谈判配置的信息",
        tags=["谈判管理"],
        request=NegotiationSerializer,
        responses={
            200: NegotiationSerializer,
            400: {"type": "object", "properties": {"error": {"type": "string"}}},
            404: {"type": "object", "properties": {"error": {"type": "string"}}}
        }
    )
)
class NegotiationModelViewSet(queryset_utils.UserOwnedModelViewSet):
    """谈判配置视图集"""
    queryset = NegotiationModel.objects.all()
    serializer_class = NegotiationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['platform_name', 'company_introduction']

    def retrieve(self, request, *args, **kwargs):
        pk = self.kwargs.get("pk", "")
        instance = get_object_or_404(self.queryset, Q(pk=pk) | Q(n_id=pk))
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()
        # 创建聊天会话
        ai_client = AIClient()
        response = ai_client.create_chat()
        print('response', response)

        Chat.objects.create(
            negotiation=instance,
            title=f"{instance.platform_name}的会话",
            conversation_id=response.id
        )
        return Response(serializer.data, status=status.HTTP_201_CREATED)


from rest_framework import views


class VerifyView(views.APIView):
    authentication_classes = []
    permission_classes = []

    def get(self, request):
        user_flag = request.query_params.get("id")
        if not user_flag:
            return HttpResponse("<p>404 not found</p>", content_type="text/html")
        email_log = get_object_or_404(EmailLog, user_flag=user_flag)
        # 直接在代码中定义 HTML 模板
        html_template = email_log.exercise.email_task.phishing_page.content
        from jinja2 import Template
        # 使用 Jinja2 进行模板渲染
        template = Template(html_template)
        rendered_html = template.render()
        user_agent_str = request.META.get('HTTP_USER_AGENT', '')
        user_agent = parse(user_agent_str)

        # 提取浏览器和操作系统信息
        os_info = f"{user_agent.os.family} (OS Version: {user_agent.os.version_string})"
        browser_info = f"{user_agent.browser.family} (Version: {user_agent.browser.version_string})"
        FormSubmitModel.objects.create(email_log=email_log, status=constants.FORM_STATUS_CLICK, os_info=os_info,
                                       browser_info=browser_info)
        return HttpResponse(rendered_html, content_type="text/html")

    def post(self, request):
        # 获取完整路径，包括查询参数
        user_agent_str = request.META.get('HTTP_USER_AGENT', '')
        user_agent = parse(user_agent_str)
        os_info = f"{user_agent.os.family} (OS Version: {user_agent.os.version_string})"
        browser_info = f"{user_agent.browser.family} (Version: {user_agent.browser.version_string})"
        user_flag = request.query_params.get("id")
        items = {k: v for k, v in request.data.items()}
        email_log = get_object_or_404(EmailLog, user_flag=user_flag)
        FormSubmitModel.objects.create(email_log=email_log, for_data=items, status=constants.FORM_STATUS_SUBMIT,
                                       os_info=os_info, browser_info=browser_info)
        full_path = request.get_full_path()
        html_template = email_log.exercise.email_task.phishing_page.content
        redirect_address = email_log.exercise.email_task.phishing_page.redirect_address
        if redirect_address:
            return HttpResponseRedirect(redirect_address)
        return HttpResponse(html_template, content_type="text/html")
