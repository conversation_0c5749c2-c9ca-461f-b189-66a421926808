from django.contrib import admin
from .models import Virus, RansomTemplate

@admin.register(Virus)
class VirusAdmin(admin.ModelAdmin):
    list_display = ('created_at', )
    list_filter = ('created_at', )
    search_fields = ('created_at', )
    date_hierarchy = 'created_at'

@admin.register(RansomTemplate)
class RansomTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'language', 'created_by', 'created_at')
    list_filter = ('language', 'created_at')
    search_fields = ('name', 'content')
    date_hierarchy = 'created_at'
