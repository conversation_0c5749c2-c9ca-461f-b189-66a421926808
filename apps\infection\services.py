from django.utils import timezone
from django.db.models import Sum, Count, Q
from .models import InfectionRecord
from apps.exercise.models import Exercise, ExerciseLog
from apps.system.services import EmailService


class InfectionService:
    """感染情况服务类
    
    处理感染记录和数据泄露的业务逻辑
    """

    @staticmethod
    def create_infection_record(exercise, asset, **kwargs):
        """创建感染记录
        
        Args:
            exercise: 演练对象
            asset: 资产对象
            **kwargs: 其他感染记录字段,包括:
                - client_id: 客户端ID
                - hostname: 主机名
                - username: 用户名
                - exec_path: 执行路径
                - ip_address: IP地址
                - location: 位置信息
                - status: 感染状态
                - description: 描述
        """
        # 创建感染记录
        record = InfectionRecord.objects.create(
            exercise=exercise,
            asset=asset,
            **kwargs
        )

        # 创建演练日志
        ExerciseLog.objects.create(
            exercise=exercise,
            event_type='IN',
            asset=asset,
            description=f'资产 {asset.name} 被感染，状态：{record.get_status_display()}'
        )

        # 发送通知
        EmailService.send_infection_notification(record)

        return record

    @staticmethod
    def update_infection_status(record, new_status, **kwargs):
        """更新感染记录状态
        
        Args:
            record: 感染记录对象
            new_status: 新状态
            **kwargs: 其他需要更新的字段
        """
        old_status = record.status

        # 更新状态和其他字段
        for key, value in kwargs.items():
            setattr(record, key, value)
        record.status = new_status
        record.save()

        # 创建演练日志
        ExerciseLog.objects.create(
            exercise=record.exercise,
            event_type='IN',
            asset=record.asset,
            description=f'资产状态从 {record.get_status_display(old_status)} 变更为 {record.get_status_display()}'
        )

        return record

    @staticmethod
    def record_data_leakage(infection_record, **kwargs):
        """记录数据泄露

        注意：DataLeakage 模型已被删除，此方法暂时不执行任何操作

        Args:
            infection_record: 感染记录对象
            **kwargs: 数据泄露记录字段
        """
        # DataLeakage 模型已被删除，暂时不执行任何操作
        # 可以在这里添加日志记录或其他替代逻辑
        return None

    @staticmethod
    def get_infection_statistics(exercise_id=None, start_date=None, end_date=None):
        """获取感染统计数据
        
        Args:
            exercise_id: 演练ID（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
        """
        queryset = InfectionRecord.objects.all()

        # 应用过滤条件
        if exercise_id:
            queryset = queryset.filter(exercise_id=exercise_id)
        if start_date:
            queryset = queryset.filter(infection_time__gte=start_date)
        if end_date:
            queryset = queryset.filter(infection_time__lte=end_date)

        # 计算统计数据
        stats = {
            'total_infected': queryset.count(),
            'total_encrypted_files': queryset.aggregate(
                total=Sum('encrypted_files')
            )['total'] or 0,
            'total_data_loss': queryset.aggregate(
                total=Sum('data_loss')
            )['total'] or 0,
            'status_distribution': dict(
                queryset.values('status')
                .annotate(count=Count('id'))
                .values_list('status', 'count')
            ),
            'department_distribution': dict(
                queryset.values('asset__department')
                .annotate(count=Count('id'))
                .values_list('asset__department', 'count')
            ),
            'asset_type_distribution': dict(
                queryset.values('asset__asset_type')
                .annotate(count=Count('id'))
                .values_list('asset__asset_type', 'count')
            )
        }

        return stats
