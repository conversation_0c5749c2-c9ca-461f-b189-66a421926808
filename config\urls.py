"""

URL configuration for config project.



The `urlpatterns` list routes URLs to views. For more information please see:

    https://docs.djangoproject.com/en/5.1/topics/http/urls/

Examples:

Function views

    1. Add an import:  from my_app import views

    2. Add a URL to urlpatterns:  path('', views.home, name='home')

Class-based views

    1. Add an import:  from other_app.views import Home

    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')

Including another URLconf

    1. Import the include() function: from django.urls import include, path

    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))

"""

from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from django.views.static import serve
from drf_spectacular.views import SpectacularAPIView, SpectacularRedocView, SpectacularSwaggerView
from rest_framework.routers import DefaultRouter
from apps.dashboard.views import DashboardViewSet

# 创建路由器
router = DefaultRouter()
router.register(r'api/v1/dashboard', DashboardViewSet, basename='dashboard')

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/document/', SpectacularAPIView.as_view(), name='schema'),
    path('api/document/swagger-ui/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/document/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    path('api/v1/auth/', include('djoser.urls')),
    path('api/v1/auth/', include('djoser.urls.jwt')),
    path('api/v1/', include('apps.users.urls')),
    path('api/v1/', include('apps.virus.urls')),
    path('api/v1/', include('apps.assets.urls')),
    path('api/v1/', include('apps.exercise.urls')),
    path('api/v1/', include('apps.infection.urls')),
    path('api/v1/', include('apps.system.urls')),
    path('api/v1/', include('apps.phishing.urls')),
    path('api/v1/family/', include('apps.family.urls')),
    path('api/v1/chat/', include('apps.chat.urls')),
] + router.urls

# 如果是debug模式，静态资源使用django自带的
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# 在非调试模式下，如果使用本地文件存储，也需要提供媒体文件服务
elif settings.FILE_STORAGE_BACKEND in ["local"]:
    urlpatterns += [
        re_path(r'^media/(?P<path>.*)$', serve, {'document_root': settings.MEDIA_ROOT}),
    ]