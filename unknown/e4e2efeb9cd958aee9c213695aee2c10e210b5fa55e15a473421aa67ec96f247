from django.db.models import Q
from django.shortcuts import get_object_or_404
from rest_framework import serializers

from .models import Infect<PERSON><PERSON><PERSON>ord, Device, DeviceCommand
from ..assets.models import Asset, AssetGroup
from ..exercise.models import Exercise
from ..system.models import NotifyModel


class DeviceSerializer(serializers.ModelSerializer):
    """设备信息序列化器"""
    hostname = serializers.SerializerMethodField()
    username = serializers.SerializerMethodField()
    ip = serializers.SerializerMethodField()
    location = serializers.SerializerMethodField()
    system_version = serializers.SerializerMethodField()

    class Meta:
        model = Device
        fields = ('device_id', 'first_seen', 'last_seen', 'infection_count',
                 'hostname', 'username', 'ip', 'location', 'system_version')
        read_only_fields = ('first_seen', 'last_seen', 'infection_count')

    def get_latest_infection_record(self, obj):
        """获取最新的感染记录"""
        return obj.infection_records.order_by('-system_time').first()

    def get_hostname(self, obj):
        """获取主机名"""
        record = self.get_latest_infection_record(obj)
        return record.hostname if record else obj.hostname

    def get_username(self, obj):
        """获取用户名"""
        record = self.get_latest_infection_record(obj)
        return record.username if record else None

    def get_ip(self, obj):
        """获取IP地址"""
        record = self.get_latest_infection_record(obj)
        return record.ip_address if record else None

    def get_location(self, obj):
        """获取位置信息"""
        record = self.get_latest_infection_record(obj)
        return record.location if record else None

    def get_system_version(self, obj):
        """获取系统版本"""
        record = obj.infection_records.order_by('-system_time').first()
        return record.system_version if record else None


class InfectionRecordSerializer(serializers.ModelSerializer):
    """感染记录序列化器"""
    device = DeviceSerializer(read_only=True)
    id = serializers.CharField(write_only=True, required=True)  # 用于接收设备ID
    ip = serializers.IPAddressField(write_only=True, required=True)  # 用于接收IP地址
    mac = serializers.CharField(write_only=True, required=True)  # 用于接收MAC地址

    class Meta:
        model = InfectionRecord
        fields = (
            'id', 'device', 'hostname', 'username', 
            'exec_path', 'system_time', 'ip', 'ip_address', 
            'location', 'system_version', 'mac'
        )
        read_only_fields = ('device', 'ip_address')

    def create(self, validated_data):
        """创建感染记录"""
        print(f"=============", validated_data)
        # 从validated_data中提取device_id和ip
        device_id = validated_data.pop('id')
        ip = validated_data.pop('ip')
        # 从资产下取出数据进行比较
        hostname = validated_data.get('hostname')
        username = validated_data.get('username')
        system_version = validated_data.get('system_version')
        mac = validated_data.get('mac')
        # ip_address = validated_data.get('ip_address')

        # 将ip赋值给ip_address
        validated_data['ip_address'] = ip
        validated_data['mac'] = mac
        # 演练下的全部资产组
        matching_exercise_ids = Exercise.objects.filter(
            Q(target_groups__asset__name=hostname) |  # 匹配资产名称
            Q(target_groups__asset__mac_address=mac),  # 匹配资产名称
            # Q(target_groups__asset__ip_address_v4=ip)  # 匹配资产 IP
        ).filter(status=Exercise.Status.RUNNING).distinct().values_list('id', flat=True)
        print(f"=============", validated_data, matching_exercise_ids)
        for exercise_id in matching_exercise_ids:
            # 获取或创建Device
            devices = Device.objects.filter(exercise_id=exercise_id)
            devices = devices.filter(Q(hostname=hostname) | Q(mac_address=mac))
            print("=============", devices.count())
            for device_obj in devices:
                print(device_id)
                # 更新设备信息
                device_obj.infection_count += 1
                device_obj.device_id = device_id
                device_obj.save()
                infection_record = InfectionRecord.objects.create(
                    exercise_id=exercise_id,
                    status='IN',
                    device=device_obj,
                    exec_path=validated_data.get("exec_path"),
                    hostname=hostname,
                    username=username,
                    ip_address=ip,
                    system_version=system_version,
                )
                exercise = get_object_or_404(Exercise, id=exercise_id)
                NotifyModel.objects.create(
                    exercise_id=exercise_id,
                    user=exercise.created_by,
                    content=f"{exercise.name}演练下的{validated_data['username']}的设备被感染"
                )

            # # 创建感染记录
            # validated_data['device'] = device
            # super().create(validated_data)
        return validated_data


class DeviceCommandSerializer(serializers.ModelSerializer):
    """设备命令序列化器"""
    device = serializers.CharField(required=False)  # 接收设备ID字符串
    exercise_id = serializers.CharField(required=False)  # 接收演练ID字符串

    class Meta:
        model = DeviceCommand
        fields = '__all__'
        read_only_fields = ['status', 'response']

    def validate(self, attrs):
        """验证命令参数"""
        command = attrs.get('command')
        args = attrs.get('args', {})
        device_id = attrs.get('device')
        exercise_id = attrs.get('exercise_id')
        print('device_id', device_id)
        print('exercise_id', exercise_id)

        # 验证all命令不需要device和其他参数
        if command == 'all':
            if device_id:
                raise serializers.ValidationError("获取在线设备命令不需要指定设备")
            if args:
                raise serializers.ValidationError("获取在线设备命令不需要其他参数")
            attrs['device'] = None
        # 其他命令需要指定device
        else:
            if not device_id:
                raise serializers.ValidationError("需要指定操作设备")
            
            # 根据device_id查找Device对象
            try:
                device = Device.objects.filter(device_id=device_id).last()
                attrs['device'] = device
            except Device.DoesNotExist:
                raise serializers.ValidationError(f"设备 {device_id} 不存在")
            
            # 验证命令参数
            if 'CMD' not in args:
                raise serializers.ValidationError("缺少必要的命令参数CMD")
        
        return attrs