from rest_framework import serializers
from .models import (
    Asset, AssetGroup, AssetImportHistory
)
from drf_spectacular.utils import extend_schema_field
from django.utils import timezone
from drf_writable_nested import WritableNestedModelSerializer


class AssetImportHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = AssetImportHistory
        fields = ['id', 'file_name', 'file_size', 'total_count', 'success_count',
                  'failed_count', 'status', 'error_message', 'created_at', 'completed_at']


class AssetSerializer(serializers.ModelSerializer):
    created_by = serializers.ReadOnlyField(source='created_by.username')
    # groups = serializers.StringRelatedField(many=True, read_only=True)
    group_name = serializers.ReadOnlyField(source='group.name')
    ip_address_v4 = serializers.Char<PERSON>ield(required=False, allow_blank=True)
    ip_address_v6 = serializers.Char<PERSON><PERSON>(required=False, allow_blank=True)
    # latest_status = serializers.SerializerMethodField()
    # status = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    os_display = serializers.CharField(source='get_os_display', read_only=True)

    class Meta:
        model = Asset
        fields = ['id', 'name', 'asset_type', 'ip_address_v4', 'ip_address_v6', 'mac_address',
                  'os_type', 'os_version', 'department', 'username',
                  'description', 'is_active', 'last_seen',
                  'created_by', 'created_at', 'updated_at', 'group', 'email', 'group_name', 'status_display',
                  'os_display']
        read_only_fields = ('created_by', 'created_at', 'updated_at',
                            'last_seen')

    @extend_schema_field({'type': 'object', 'properties': {
        'cpu_usage': {'type': 'number'},
        'memory_usage': {'type': 'number'},
        'disk_usage': {'type': 'number'},
        'network_in': {'type': 'number'},
        'network_out': {'type': 'number'},
        'process_count': {'type': 'integer'},
        'timestamp': {'type': 'string', 'format': 'date-time'}
    }})
    # def get_latest_status(self, obj: Asset) -> dict:
    #     """获取最新状态"""
    #     latest_status = obj.statuses.order_by('-timestamp').first()
    #     if latest_status:
    #         return AssetStatusSerializer(latest_status).data
    #     return None

    @extend_schema_field({'type': 'string', 'enum': ['online', 'offline', 'unknown']})
    # def get_status(self, obj: Asset) -> str:
    #     """获取资产状态"""
    #     if not obj.is_active:
    #         return 'offline'
    #     latest_status = obj.statuses.order_by('-timestamp').first()
    #     if latest_status and (timezone.now() - latest_status.timestamp).seconds < 300:
    #         return 'online'
    #     return 'unknown'

    def create(self, validated_data):
        user = self.context["request"].user
        validated_data["created_by"] = user
        tag_ids = validated_data.pop('tag_ids', [])
        asset = Asset.objects.create(**validated_data)
        if tag_ids:
            asset.tags.set(tag_ids)
        return asset

    def update(self, instance, validated_data):
        tag_ids = validated_data.pop('tag_ids', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        if tag_ids is not None:
            instance.tags.set(tag_ids)
        return instance


class AssetGroupSerializer(WritableNestedModelSerializer):
    # created_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True, required=False)
    # updated_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True, required=False)
    asset_count = serializers.SerializerMethodField(label="资产数量", read_only=True)
    assets = AssetSerializer(many=True, source="asset", read_only=True)

    def get_asset_count(self, obj):
        return obj.asset.count()

    class Meta:
        model = AssetGroup
        fields = ['id', 'name', 'description', 'asset_count', 'created_at', 'updated_at', 'assets']
        read_only_fields = ('created_at', 'updated_at')

    def create(self, validated_data: dict) -> AssetGroup:
        """创建资产组"""
        user = self.context["request"].user
        validated_data["created_by"] = user
        return AssetGroup.objects.create(**validated_data)

    def update(self, instance: AssetGroup, validated_data: dict) -> AssetGroup:
        """更新资产组"""
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance



class ExerciseAssetSerializer(serializers.ModelSerializer):
    created_by = serializers.ReadOnlyField(source='created_by.username')
    group_name = serializers.ReadOnlyField(source='group.name')
    ip_address_v4 = serializers.CharField(required=False, allow_blank=True)
    ip_address_v6 = serializers.CharField(required=False, allow_blank=True)
    is_infected = serializers.SerializerMethodField()


    class Meta:
        model = Asset
        fields = ['id', 'name', 'asset_type', 'ip_address_v4', 'ip_address_v6', 'mac_address',
                  'os_type', 'os_version', 'department', 'username',
                  'description', 'is_active', 'last_seen',
                  'created_by', 'created_at', 'updated_at', 'group', 'email', 'is_infected', 'group_name']
        read_only_fields = ('created_by', 'created_at', 'updated_at',
                            'last_seen')

    def get_is_infected(self, obj):
        from apps.infection.models import Device
        exercise_id = self.context["request"].parser_context["kwargs"].get('exercise_id')

        # 查这个资产是否有感染记录
        device = Device.objects.filter(hostname=obj.name, exercise_id=exercise_id).order_by('-created_at').first()
        if not device:
            return False
        if not device.infection_count:
            return False
        if device.infection_count > 0:
            return True
        return True
